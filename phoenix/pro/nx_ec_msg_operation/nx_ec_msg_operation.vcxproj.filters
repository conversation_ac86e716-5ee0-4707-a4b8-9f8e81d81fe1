﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{4374221d-3cd0-4ec6-b45d-a7839a041480}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="NXEcMsgOperationObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcSrvMsgOperaObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXECObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="nxec_msg_opra_export.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcCliMsgOperaObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXEcRegisterObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXObserver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXLoadSrvMedLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXSubject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ec_msg_opera_modify_note.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NxYKStrapRead.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\platform_include\plm_diomngr\DllMngr_PlmDioMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\ec_common\ec_common_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\INXEcMsgOperationObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcMsgOperationObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcSrvMsgOperaObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXECObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcCliMsgOperaObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXEcRegisterObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXLoadEcModelLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXObserver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXLoadSrvMedLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXSubject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\IniOperate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NxYKStrapRead.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_msg_operation.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>