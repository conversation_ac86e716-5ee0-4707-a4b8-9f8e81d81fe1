﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E48B4965-D038-415B-A5FF-FABBB0615D02}</ProjectGuid>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <Keyword>ManagedCProj</Keyword>
    <RootNamespace>nx_ec_msg_operation</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../../../../../platform_include/plm_diomngr/</IncludePath>
    <OutDir>..\..\..\..\..\nx_bin\$(Configuration)\nx_ec\</OutDir>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;../../../../../nx_lib/debug/;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../../../../../platform_include/plm_diomngr/</IncludePath>
    <OutDir>..\..\..\..\..\nx_bin\$(Configuration)\nx_ec\</OutDir>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;../../../../../nx_lib/release/;</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_MSG_OPERATION_EXPORT;_USE_32BIT_TIME_T</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_db.lib</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy ..\..\ecpro.ini $(OutDir). /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_MSG_OPERATION_EXPORT;_USE_32BIT_TIME_T</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_db.lib</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy ..\..\ecpro.ini $(OutDir). /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\..\..\..\platform_include\plm_diomngr\DllMngr_PlmDioMngr.cpp" />
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp" />
    <ClCompile Include="..\..\ec_common\NXECObject.cpp" />
    <ClCompile Include="..\..\ec_common\NXEcRegisterObject.cpp" />
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp" />
    <ClCompile Include="..\..\ec_common\NXLoadSrvMedLib.cpp" />
    <ClCompile Include="..\..\ec_common\NXObserver.cpp" />
    <ClCompile Include="..\..\ec_common\NXSubject.cpp" />
    <ClCompile Include="ec_msg_opera_modify_note.cpp" />
    <ClCompile Include="NXEcCliMsgOperaObj.cpp" />
    <ClCompile Include="NXEcMsgOperationObj.cpp" />
    <ClCompile Include="NXEcSrvMsgOperaObj.cpp" />
    <ClCompile Include="nxec_msg_opra_export.cpp" />
    <ClCompile Include="NxYKStrapRead.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\platform_include\IniOperate.h" />
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h" />
    <ClInclude Include="..\..\ec_common\ec_common_def.h" />
    <ClInclude Include="..\..\ec_common\NXECObject.h" />
    <ClInclude Include="..\..\ec_common\NXEcRegisterObject.h" />
    <ClInclude Include="..\..\ec_common\NXLoadEcModelLib.h" />
    <ClInclude Include="..\..\ec_common\NXLoadSrvMedLib.h" />
    <ClInclude Include="..\..\ec_common\NXObserver.h" />
    <ClInclude Include="..\..\ec_common\NXSubject.h" />
    <ClInclude Include="..\ec_pro_common\INXEcMsgOperationObj.h" />
    <ClInclude Include="NXEcCliMsgOperaObj.h" />
    <ClInclude Include="NXEcMsgOperationObj.h" />
    <ClInclude Include="NXEcSrvMsgOperaObj.h" />
    <ClInclude Include="NxYKStrapRead.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_msg_operation.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>