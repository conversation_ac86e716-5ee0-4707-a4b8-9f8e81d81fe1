﻿/*********************************************************************
*ec_srv_hz103_modify_note.cpp       creater:yys     create date:09/03/2015
*-------------------------------------------------------------
*               note: 华中103服务端规约转换模块历史修改记录
*  
*********************************************************************/
/**	Ver1.0.0 2017-11-16	修改人:杨轶森
修改内容:
	首次创建.
-----
----- 发布说明:
----- 首次创建.
----- 
*/  
/* Ver 1.0.1
2017-11-27   宋亮： 修改NXEc60870CvtObj_HZS.cpp：_GetAsdu13CvtType（）对召唤录波文件命令长度的判断，由49改为46.
					初始化设备时，按指定长度生成字符流，不足补0.原先没有补0.
*/
/**	Ver1.0.2 2017-12-08	修改人:杨轶森
修改内容:
	修改主站通过总召召唤软压板，子站响应上送。涉及类NXEcProAsdu42_HZS。
-----
----- 发布说明:
----- 修改主站通过总召召唤软压板，子站响应上送。
----- 
*/ 
/**	Ver1.0.3 2017-12-11	修改人:杨轶森
修改内容:
	修改装置属性结构处理函数。增加传入值的判断，解决当传入值为空，不处理的情况。涉及类__AddOtherDataToStructX。
-----
----- 发布说明:
----- 初始化装置树形结构时，增加传入值的判断，解决当传入值为空，不处理的情况。
----- 
*/ 
/**	Ver1.0.4 2017-12-11	修改人:杨轶森
修改内容:
	兼容四方主站下发总召命令时，上送所有CPU信息；解决召唤录波召唤失败问题。
-----
----- 发布说明:
----- 兼容四方主站下发总召命令时，上送所有CPU信息；解决召唤录波召唤失败问题。
----- 
*/ 
/**	Ver1.0.5 2021-04-12	修改人:杨轶森
1、修改内容:
（1）处理响应主站召唤录波文件时，上送录波文件名后缀前会多一个“.”的BUG.

 2、影响范围：
  无。
  3、修改代码范围：
  （1）本次修改了NXEcProAsdu13_HZS.cpp共计1个文件；
  （2）涉及修改：TNXEcProAsdu13HZS::_gw_AddOneFileToTempFile(),TNXEcProAsdu13HZS::_CreateTempWaveFileForAsdu14()
**/ 
/**	Ver1.0.6 2021-07-28	修改人:杨轶森
1、修改内容:
（1）针对华中103V1.1规范中录波文件数据格式做兼容处理。先填写各个文件的扩展名及文件大小后，再填写文件内容。

 2、影响范围：
  无。
  3、修改代码范围：
  （1）本次修改了NXEcProAsdu13_HZS.cpp共计1个文件；
  （2）涉及修改：TNXEcProAsdu13HZS::_gw_AddOneFileToTempFile(),TNXEcProAsdu13HZS::_gw_AddOneExtToTempFile()
**/ 
/**	Ver1.0.7 2021-07-28	修改人:杨轶森
1、修改内容（220kV龙凤坝发现）:
（1）针对华中103V1.1规范中定值属性结构内容重新填写，增加定值单编号属性结构。

 2、影响范围：
  四方主站初始化时，量纲实际值错误的问题。
  3、修改代码范围：
  （1）本次修改了TNXEcProAsdu21HZS.cpp共计1个文件；
  （2）涉及修改：TNXEcProAsdu21HZS::_CvtSgItemCfgtoDataStruct(),TNXEcProAsdu21HZS::__SplitStr()
**/ 