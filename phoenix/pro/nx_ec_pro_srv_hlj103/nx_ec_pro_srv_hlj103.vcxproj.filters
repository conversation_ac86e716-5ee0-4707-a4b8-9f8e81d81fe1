﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="NXEc60870CvtFactory_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEc60870CvtObj_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcIec103ExplainFactory_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcHLJ103SrvProtocol.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu1_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXECObject.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ExplainFactory.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ProExplain.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ec_pro_srv_hlj103_export.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ec_srv_hlj103_modify_note.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu18.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu102.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu21_Direct_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinystr.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxml.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxmlerror.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxmlparser.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\UnLibmngr_Gbk2Utf8.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu7_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu13_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu10_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu2_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu16_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu15_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu12_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu103_HLJ.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="NXEc60870CvtFactory_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEc60870CvtObj_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu1_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu21_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcHLJ103SrvProtocol.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcIec103ExplainFactory_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu7_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu2_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu13_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu10_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu16_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu15_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu12_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu103_HLJ.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
</Project>