# ASDU10 RII区分功能修改设计文档

## 1. 业务需求
根据福建103规约要求，ASDU10消息中需要区分两种不同类型的信息：
- **故障量信息帧**：返回信息标识符(RII)填253（0xFD）
- **当前运行定值区变化信息帧**：返回信息标识符(RII)填254（0xFE）

## 2. 原实现分析

### 2.1 原有问题
1. **缺少RII区分**：所有ASDU10消息的RII都设置为0，无法区分信息类型
2. **硬编码问题**：在`FormatAsdu10BodyAutoUp`中硬编码传递RII=0

### 2.2 结构体分析
通过代码分析发现：
- `ASDU10_INFO`结构体中没有`nRii`成员变量
- RII值通过函数参数传递给`_FormatOneGroupAsdu10BodyAutoUp`函数
- 需要在调用链的上层区分不同类型的信息处理

## 3. 解决方案设计

### 3.1 实现方案

#### 3.1.1 最小修改方案设计
基于代码复用原则，采用函数重载的方式实现RII区分功能：

**新增支持RII参数的重载函数**：
```cpp
// 在现有FormatAsdu10BodyAutoUp函数基础上，新增支持RII参数的重载版本
int FormatAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN u_int8 nRii,IN int nReserve=0);
```

#### 3.1.2 修改现有函数逻辑
```cpp
// 在__CvtAsdu10InfoToProBodyAuToUp函数中添加消息类型判断逻辑
int TNXEcProAsdu10GWS::__CvtAsdu10InfoToProBodyAuToUp(...)
{
    // ... 原有代码保持不变 ...
    
    // 根据消息类型选择对应的RII值
    u_int8 nRii = 0;  // 默认RII值为0（保持原有行为）
    if (m_pEventMsg != NULL) {
        switch(m_pEventMsg->n_msg_type) {
            case NX_IED_EVENT_SOFTTRAP_REPORT:         // 故障量信息
                nRii = 0xFD;  // 故障量信息帧 RII = 253 (FDH)
                break;
            case NX_SYS_EVENT_IED_SGZONE_CHG_REPORT:   // 定值区变化信息
                nRii = 0xFE;  // 定值区变化信息帧 RII = 254 (FEH)
                break;
            default:
                nRii = 0;     // 其他消息类型使用默认RII=0
                break;
        }
    }
    
    // 调用支持RII参数的重载函数
    FormatAsdu10BodyAutoUp(Asdu10Info,lResult,nRii);
    
    // ... 原有代码保持不变 ...
}
```

#### 3.1.3 重载函数实现细节
```cpp
// 新的重载函数实现
int TNXEcProAsdu10GWS::FormatAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN u_int8 nRii,IN int nReserve)
{
    // 格式化所有组自动上送（使用指定的RII值）
    iteMap = Asdu10Info.GroupToGenListMap.begin();
    while( iteMap != Asdu10Info.GroupToGenListMap.end() )
    {
        _FormatOneGroupAsdu10BodyAutoUp(Asdu10Info,iteMap->first,nRii,lBody);
        // ... 其他处理逻辑与原函数完全相同 ...
    }
}
```

## 4. 文件修改清单

### 4.1 新增函数列表

#### 在NXEcProAsdu10_GWS.h中添加的声明：
```cpp
// 新增支持RII参数的重载函数声明
virtual int FormatAsdu10BodyAutoUp(IN ASDU10_INFO &Asdu10Info,OUT PRO_FRAME_BODY_LIST &lBody,IN u_int8 nRii,IN int nReserve=0);
```

#### 在NXEcProAsdu10_GWS.cpp中实现的函数：
- `FormatAsdu10BodyAutoUp(支持RII参数的重载版本)` - 支持指定RII值的格式化函数

### 4.2 修改的现有函数
- `__CvtAsdu10InfoToProBodyAuToUp()` - 添加消息类型判断逻辑，根据不同消息类型设置相应RII值
