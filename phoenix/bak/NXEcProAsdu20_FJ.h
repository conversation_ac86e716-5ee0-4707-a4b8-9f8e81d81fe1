/**********************************************************************
* NXEcProAsdu20_FJ.h         author:ml     date:2025/08/12            
*---------------------------------------------------------------------
*  note: 福建103 ASDU20报文转换处理头文件 - 远方复归二次设备指示灯                                                              
*  
**********************************************************************/

#ifndef _H_NXECPROASDU20_FJ_H_ 
#define _H_NXECPROASDU20_FJ_H_

#include "NXEcProAsdu.h"

/**
* @defgroup  福建103 TNXEcProAsdu20FJ:ASDU20报文转换处理结点类
* @{
*/
 
class TNXEcProAsdu20FJ:public TNXEcProAsdu
{
    ///////////////////////////////////////////////////////////////构造、析构
public:
    
    /**
    * @brief         析构函数
    * @param[in]     无 
    * @param[out]    无
    * @return        无
    */
    virtual ~TNXEcProAsdu20FJ();

    /**
    * @brief         构造函数 
    * @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
    * @param[out]    CLogRecord * pLogRecord:日志对象指针
    * @return        无
    */
    TNXEcProAsdu20FJ(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);

    ///////////////////////////////////////////////////////////////公用方法
public:
    
    /**
    * @brief         转换规约信息到NX通用消息结构
    * @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
    * @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
    * @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应(服务端规约有效）
    * @return        >=0:成功 <0:失败
    */
    virtual int ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lResult);

protected:
    
    /**
    * @brief         福建103全站复归处理（复用南网逻辑）
    * @param[in]     u_int8 nRii :返回信息标识符
    * @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
    */
    void _CvtWholeStationReset(IN u_int8 nRii, OUT NX_COMMON_MSG_LIST & lMsg);
    
    /**
    * @brief         福建103单装置复归处理（适配ASDU地址解析）
    * @param[in]     u_int16 nAsduAddr :ASDU地址（高8位为103地址）
    * @param[in]     u_int8 nRii :返回信息标识符
    * @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
    * @return        bool true-成功 false-失败
    */
    bool _CvtOneDevReset(IN u_int16 nAsduAddr, IN u_int8 nRii, OUT NX_COMMON_MSG_LIST & lMsg);
    
    /**
    * @brief         生成设备复归通用消息（复用南网逻辑）
    * @param[in]     int nIedId :设备ID
    * @param[in]     u_int8 nRii :返回信息标识符
    * @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
    * @return        int 0-成功 其它失败
    */
    int _MakeResetCommonMsg(IN int nIedId, IN u_int8 nRii, OUT NX_COMMON_MSG_LIST & lMsg);
    
    /**
    * @brief         生成ASDU1复归回应报文（根据执行结果返回成功或失败）
    * @param[in]     PRO_FRAME_BODY & OrigCmd:原命令
    * @param[out]    PRO_FRAME_BODY_LIST & lResult :生成的回应报文列表
    * @param[in]     bool bSuccess:执行是否成功
    * @return        int 0-成功 其它失败
    */
    int _MakeResetResponse(IN PRO_FRAME_BODY & OrigCmd, OUT PRO_FRAME_BODY_LIST & lResult, IN bool bSuccess);

    ////////////////////////////////////////////////////////////////////////保护成员
protected:
};

/** @} */ //OVER

#endif  // _H_NXECPROASDU20_FJ_H_
