format:
  tab_size: 2
  line_width: 100
  dangle_parens: true

parse:
  additional_commands:
    cpmaddpackage:
      pargs:
        nargs: '*'
        flags: []
      spelling: CPMAddPackage
      kwargs: &cpmaddpackagekwargs
        NAME: 1
        FORCE: 1
        VERSION: 1
        GIT_TAG: 1
        DOWNLOAD_ONLY: 1
        GITHUB_REPOSITORY: 1
        GITLAB_REPOSITORY: 1
        GIT_REPOSITORY: 1
        SVN_REPOSITORY: 1
        SVN_REVISION: 1
        SOURCE_DIR: 1
        DOWNLOAD_COMMAND: 1
        FIND_PACKAGE_ARGUMENTS: 1
        NO_CACHE: 1
        GIT_SHALLOW: 1
        URL: 1
        URL_HASH: 1
        URL_MD5: 1
        DOWNLOAD_NAME: 1
        DOWNLOAD_NO_EXTRACT: 1
        HTTP_USERNAME: 1
        HTTP_PASSWORD: 1
        OPTIONS: +
    cpmfindpackage:
      pargs:
        nargs: '*'
        flags: []
      spelling: CPMFindPackage
      kwargs: *cpmaddpackagekwargs
    packageproject:
      pargs:
        nargs: '*'
        flags: []
      spelling: packageProject
      kwargs:
        NAME: 1
        VERSION: 1
        NAMESPACE: 1
        INCLUDE_DIR: 1
        INCLUDE_DESTINATION: 1
        BINARY_DIR: 1
        COMPATIBILITY: 1
        VERSION_HEADER: 1
        DEPENDENCIES: +
