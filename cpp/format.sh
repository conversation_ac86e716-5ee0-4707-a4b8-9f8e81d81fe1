#!/bin/bash

# 代码格式化脚本

set -e

echo "🎨 开始格式化C++代码..."

# 格式化源文件
echo "格式化源文件..."
find source -name "*.cpp" -exec clang-format -i {} \;

# 格式化头文件
echo "格式化头文件..."
find include -name "*.hpp" -o -name "*.h" -exec clang-format -i {} \;

# 格式化应用程序文件
echo "格式化应用程序文件..."
find app -name "*.cpp" -exec clang-format -i {} \;

# 格式化测试文件（如果存在）
if [ -d "test" ] && [ "$(find test -name "*.cpp" | wc -l)" -gt 0 ]; then
    echo "格式化测试文件..."
    find test -name "*.cpp" -exec clang-format -i {} \;
fi

echo "✅ 代码格式化完成！"
