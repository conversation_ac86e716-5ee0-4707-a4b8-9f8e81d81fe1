#include "core.hpp"
#include "spdlog/spdlog.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include <iostream>
#include <filesystem>

// 全局变量，用于跟踪初始化状态
static bool g_core_initialized = false;

// 构造函数属性，确保在动态库加载时自动调用
__attribute__((constructor))
static void core_library_constructor() {
    if (!g_core_initialized) {
        initialize_core_library();
    }
}

// 析构函数属性，确保在动态库卸载时自动调用
__attribute__((destructor))
static void core_library_destructor() {
    if (g_core_initialized) {
        cleanup_core_library();
    }
}

void initialize_core_library() {
    if (g_core_initialized) {
        return; // 已经初始化过了
    }
    
    try {
        // 确保logs目录存在
        std::filesystem::create_directories("logs");
        
        // 创建同步文件logger并设置为默认logger
        auto file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>("logs/zexuan.log");
        auto file_logger = std::make_shared<spdlog::logger>("default", file_sink);
        file_logger->set_level(spdlog::level::debug);
        file_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v");

        // 设置为同步模式
        file_logger->flush_on(spdlog::level::debug);

        // 设置为默认logger，这样所有spdlog::info()等API都会输出到文件
        spdlog::set_default_logger(file_logger);
        
        // 记录初始化成功
        spdlog::info("Core library initialized successfully");
        
        g_core_initialized = true;

    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "Failed to initialize net logger: " << ex.what() << std::endl;
        
        // 尝试创建同步控制台 logger 作为备选
        try {
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            auto console_logger = std::make_shared<spdlog::logger>("default", console_sink);
            console_logger->set_level(spdlog::level::debug);
            console_logger->flush_on(spdlog::level::debug);
            console_logger->warn("Using console logger as fallback");

            // 将控制台logger设置为默认logger
            spdlog::set_default_logger(console_logger);
            
            g_core_initialized = true;
        } catch (...) {
            std::cerr << "Failed to create fallback logger" << std::endl;
        }
    }
}

void cleanup_core_library() {
    if (!g_core_initialized) {
        return; // 没有初始化过
    }

    // 简单地标记为未初始化，让spdlog自己处理析构
    // 同步日志模式下不需要手动清理
    g_core_initialized = false;

    std::cout << "Zexuan Core Library cleanup completed" << std::endl;
}
