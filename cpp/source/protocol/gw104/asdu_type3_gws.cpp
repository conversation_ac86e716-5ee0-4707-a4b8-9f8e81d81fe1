#include "gw104/asdu_type3_gws.hpp"
#include "zexuan/base/message.hpp"
#include <spdlog/spdlog.h>
#include <chrono>
#include <ctime>

namespace zexuan {
namespace protocol {
namespace transform {
namespace gw104 {

// AsduType3GWS专门用于事件上报，不实现CommonMessage相关转换
// 使用基类的默认实现（返回-1表示不支持）

int AsduType3GWS::ConvertFromEventMessage(const base::EventMessage& event_msg,
                                        base::ProtocolFrame& frame) {
    spdlog::debug("ASDU Type3: Converting EventMessage to ProtocolFrame for timestamp event");

    try {
        // 创建符合 IEC 60870-5-103 协议格式的消息
        base::Message message;

        // 设置消息头部字段
        message.setTyp(GetSupportedType());     // TYP: ASDU类型标识 = 3
        message.setVsq(0x01);                   // VSQ: 可变结构限定词，单个信息元素
        message.setCot(0x03);                   // COT: 传送原因，突发(自发)
        message.setSource(static_cast<uint8_t>(event_msg.source_id & 0xFF)); // 源地址
        message.setTarget(0x00);                // 目标地址，0表示广播
        message.setFun(0xF0);                   // FUN: 功能类型，时间戳事件
        message.setInf(static_cast<uint8_t>(event_msg.event_type & 0xFF)); // INF: 信息序号，使用事件类型

        // 构建可变结构体数据
        std::vector<uint8_t> variable_data;

        // 添加当前时间戳（7字节 IEC 时间格式）
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::tm* local_time = std::localtime(&time_t);

        // IEC 60870-5-103 时间格式（7字节）
        uint16_t milliseconds = static_cast<uint16_t>(ms.count());
        variable_data.push_back(static_cast<uint8_t>(milliseconds & 0xFF));        // 毫秒低字节
        variable_data.push_back(static_cast<uint8_t>((milliseconds >> 8) & 0xFF)); // 毫秒高字节
        variable_data.push_back(static_cast<uint8_t>(local_time->tm_min));         // 分钟
        variable_data.push_back(static_cast<uint8_t>(local_time->tm_hour));        // 小时
        variable_data.push_back(static_cast<uint8_t>(local_time->tm_mday));        // 日
        variable_data.push_back(static_cast<uint8_t>(local_time->tm_mon + 1));     // 月
        variable_data.push_back(static_cast<uint8_t>((local_time->tm_year + 1900) - 2000)); // 年

        // 添加事件数据
        if (!event_msg.data.empty()) {
            variable_data.insert(variable_data.end(), event_msg.data.begin(), event_msg.data.end());
        }

        // 设置可变结构体数据
        message.setVariableStructure(variable_data);

        // 序列化消息到协议帧
        frame.data.clear();
        size_t serialized_size = message.serialize(frame.data);

        // 设置协议帧属性
        frame.type = GetSupportedType();
        frame.frame_id = event_msg.source_id;
        frame.cot = 0x03; // 突发(自发)

        spdlog::debug("ASDU Type3: Successfully converted EventMessage using Message::serialize(), "
                     "event_type={}, serialized_size={}, timestamp=20{:02d}-{:02d}-{:02d} {:02d}:{:02d}.{:03d}",
                     event_msg.event_type, serialized_size,
                     (local_time->tm_year + 1900) - 2000, local_time->tm_mon + 1, local_time->tm_mday,
                     local_time->tm_hour, local_time->tm_min, milliseconds);

        return 0;

    } catch (const std::exception& e) {
        spdlog::error("ASDU Type3: Exception in ConvertFromEventMessage: {}", e.what());
        return -1;
    }
}

} // namespace gw104
} // namespace transform
} // namespace protocol
} // namespace zexuan