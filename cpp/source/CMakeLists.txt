# 创建 core 库
add_library(core SHARED)

# 设置 core 的输出目录与版本
set_target_properties(core PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 添加源文件
file(GLOB_RECURSE CORE_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/base/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/net/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/platform/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/utils/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/core.cpp"
)

target_sources(core
    PRIVATE
        ${CORE_SOURCES}
)

# 设置包含目录
target_include_directories(core
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

# 创建 protocol 库
add_library(protocol SHARED)

# 设置 protocol 的输出目录与版本
set_target_properties(protocol PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 添加 protocol 源文件
file(GLOB_RECURSE PROTOCOL_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/protocol/*.cpp"
)

target_sources(protocol
    PRIVATE
        ${PROTOCOL_SOURCES}
)

# 设置 protocol 包含目录
target_include_directories(protocol
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

# protocol 依赖 core
target_link_libraries(protocol
    PUBLIC
        core
)

# 添加 Format.cmake
CPMAddPackage("gh:TheLartians/Format.cmake@1.8.2")

