# ---- Z<PERSON>uan Core Library ----

# Collect all source files
file(GLOB_RECURSE ZEXUAN_CORE_SOURCES
  CONFIGURE_DEPENDS
  "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/*.cpp"
  "${CMAKE_CURRENT_SOURCE_DIR}/core.cpp"
)

file(GLOB_RECURSE ZEXUAN_PROTOCOL_SOURCES
  CONFIGURE_DEPENDS
  "${CMAKE_CURRENT_SOURCE_DIR}/protocol/*.cpp"
)

# Create core library
add_library(zexuan-core SHARED ${ZEXUAN_CORE_SOURCES})

# Set library properties
set_target_properties(zexuan-core PROPERTIES
  VERSION ${PROJECT_VERSION}
  SOVERSION ${PROJECT_VERSION_MAJOR}
  OUTPUT_NAME "zexuan-core"
)

# Include directories
target_include_directories(zexuan-core
  PUBLIC
    $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
  PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link system libraries (if needed)
if(UNIX)
  target_link_libraries(zexuan-core PRIVATE pthread)
endif()

# Create protocol library
add_library(zexuan-protocol SHARED ${ZEXUAN_PROTOCOL_SOURCES})

set_target_properties(zexuan-protocol PROPERTIES
  VERSION ${PROJECT_VERSION}
  SOVERSION ${PROJECT_VERSION_MAJOR}
  OUTPUT_NAME "zexuan-protocol"
)

target_include_directories(zexuan-protocol
  PUBLIC
    $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
  PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Protocol library depends on core library
target_link_libraries(zexuan-protocol PUBLIC zexuan-core)

# Create an alias for easier usage
add_library(zexuan::core ALIAS zexuan-core)
add_library(zexuan::protocol ALIAS zexuan-protocol)

# ---- Installation rules ----
include(GNUInstallDirs)

install(TARGETS zexuan-core zexuan-protocol
  EXPORT zexuanTargets
  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
  ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
  RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
  INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

# Install headers
install(DIRECTORY ${PROJECT_SOURCE_DIR}/include/
  DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
  FILES_MATCHING PATTERN "*.hpp" PATTERN "*.h"
)

# Export targets
install(EXPORT zexuanTargets
  FILE zexuanTargets.cmake
  NAMESPACE zexuan::
  DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/zexuan
)
