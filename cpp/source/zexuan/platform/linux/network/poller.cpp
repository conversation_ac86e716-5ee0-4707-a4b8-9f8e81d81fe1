#include "zexuan/platform/network/poller_impl.hpp"
#include "spdlog/spdlog.h"
#include "zexuan/net/channel.hpp"
#ifdef __linux__
#include <assert.h>
#include <errno.h>
#include <sys/epoll.h>
#include <poll.h>
#include <unistd.h>
#include <cstring>
#include <cstdlib>

using namespace zexuan::platform::network;

// 常量定义
namespace {
const int kNew = -1;
const int kAdded = 1;
const int kDeleted = 2;
}

// ==================== EPollPoller 实现 ====================

// On Linux, the constants of poll(2) and epoll(4) are expected to be the same.
static_assert(EPOLLIN == POLLIN, "epoll uses same flag values as poll");
static_assert(EPOLLPRI == POLLPRI, "epoll uses same flag values as poll");
static_assert(EPOLLOUT == POLLOUT, "epoll uses same flag values as poll");
static_assert(EPOLLRDHUP == POLLRDHUP, "epoll uses same flag values as poll");
static_assert(EPOLLERR == POLLERR, "epoll uses same flag values as poll");
static_assert(EPOLLHUP == POLLHUP, "epoll uses same flag values as poll");

EPollPoller::EPollPoller()
    : epollfd_(::epoll_create1(EPOLL_CLOEXEC)),
      events_(kInitEventListSize)
{
    if (epollfd_ < 0) {
        spdlog::critical("EPollPoller::EPollPoller: {}", strerror(errno));
    }
}

EPollPoller::~EPollPoller()
{
    ::close(epollfd_);
}

Poller::Timestamp EPollPoller::poll(int timeoutMs, ChannelList* activeChannels)
{
    spdlog::trace("fd total count {}", channels_.size());
    int numEvents = ::epoll_wait(epollfd_,
                                &*events_.begin(),
                                static_cast<int>(events_.size()),
                                timeoutMs);
    int savedErrno = errno;
    Timestamp now = std::chrono::system_clock::now();
    
    if (numEvents > 0) {
        spdlog::trace("{} events happened", numEvents);
        fillActiveChannels(numEvents, activeChannels);
        if (static_cast<size_t>(numEvents) == events_.size()) {
            events_.resize(events_.size() * 2);
        }
    } else if (numEvents == 0) {
        spdlog::trace("nothing happened");
    } else {
        // error happens, log uncommon ones
        if (savedErrno != EINTR) {
            errno = savedErrno;
            spdlog::error("EPollPoller::poll(): {}", strerror(savedErrno));
        }
    }
    return now;
}

void EPollPoller::fillActiveChannels(int numEvents, ChannelList* activeChannels) const
{
    assert(static_cast<size_t>(numEvents) <= events_.size());
    for (int i = 0; i < numEvents; ++i) {
        zexuan::net::Channel* channel = static_cast<zexuan::net::Channel*>(events_[i].data.ptr);
#ifndef NDEBUG
        int fd = channel->fd();
        ChannelMap::const_iterator it = channels_.find(fd);
        assert(it != channels_.end());
        assert(it->second == channel);
#endif
        channel->set_revents(events_[i].events);
        activeChannels->push_back(channel);
    }
}

void EPollPoller::updateChannel(zexuan::net::Channel* channel)
{
    const int index = channel->index();
    spdlog::trace("fd = {} events = {} index = {}", channel->fd(), channel->events(), index);
    
    if (index == kNew || index == kDeleted) {
        // a new one, add with EPOLL_CTL_ADD
        int fd = channel->fd();
        if (index == kNew) {
            assert(channels_.find(fd) == channels_.end());
            channels_[fd] = channel;
        } else // index == kDeleted
        {
            assert(channels_.find(fd) != channels_.end());
            assert(channels_[fd] == channel);
        }

        channel->set_index(kAdded);
        update(EPOLL_CTL_ADD, channel);
    } else {
        // update existing one with EPOLL_CTL_MOD/DEL
        int fd = channel->fd();
        (void)fd;
        assert(channels_.find(fd) != channels_.end());
        assert(channels_[fd] == channel);
        assert(index == kAdded);
        if (channel->isNoneEvent()) {
            update(EPOLL_CTL_DEL, channel);
            channel->set_index(kDeleted);
        } else {
            update(EPOLL_CTL_MOD, channel);
        }
    }
}

void EPollPoller::removeChannel(zexuan::net::Channel* channel)
{
    int fd = channel->fd();
    spdlog::trace("fd = {}", fd);
    assert(channels_.find(fd) != channels_.end());
    assert(channels_[fd] == channel);
    assert(channel->isNoneEvent());
    int index = channel->index();
    assert(index == kAdded || index == kDeleted);
    size_t n = channels_.erase(fd);
    (void)n;
    assert(n == 1);

    if (index == kAdded) {
        update(EPOLL_CTL_DEL, channel);
    }
    channel->set_index(kNew);
}

bool EPollPoller::hasChannel(zexuan::net::Channel* channel) const
{
    ChannelMap::const_iterator it = channels_.find(channel->fd());
    return it != channels_.end() && it->second == channel;
}

void EPollPoller::update(int operation, zexuan::net::Channel* channel)
{
    struct epoll_event event;
    std::memset(&event, 0, sizeof event);
    event.events = channel->events();
    event.data.ptr = channel;
    int fd = channel->fd();
    spdlog::trace("epoll_ctl op = {} fd = {} event = {{ {} }}",
                  operationToString(operation), fd, channel->eventsToString());
    if (::epoll_ctl(epollfd_, operation, fd, &event) < 0) {
        if (operation == EPOLL_CTL_DEL) {
            spdlog::error("epoll_ctl op = {} fd = {}: {}", operationToString(operation), fd, strerror(errno));
        } else {
            spdlog::critical("epoll_ctl op = {} fd = {}: {}", operationToString(operation), fd, strerror(errno));
        }
    }
}

const char* EPollPoller::operationToString(int op)
{
    switch (op) {
        case EPOLL_CTL_ADD:
            return "ADD";
        case EPOLL_CTL_DEL:
            return "DEL";
        case EPOLL_CTL_MOD:
            return "MOD";
        default:
            assert(false && "ERROR op");
            return "Unknown Operation";
    }
}

// ==================== 工厂方法实现 ====================

std::unique_ptr<Poller> Poller::createDefaultPoller()
{
    
    return std::make_unique<EPollPoller>();

}
#endif