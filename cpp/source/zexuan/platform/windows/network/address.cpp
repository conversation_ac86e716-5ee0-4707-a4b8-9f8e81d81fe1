#include "zexuan/platform/network/address.hpp"

#ifdef Z<PERSON>UAN_WINDOWS
#include <cstring>

namespace zexuan {
namespace platform {
namespace network {

Address::Address() {
    std::memset(&addr_, 0, sizeof(addr_));
    addr_.sin_family = AF_INET;
}

Address::Address(uint16_t port, bool loopbackOnly) {
    std::memset(&addr_, 0, sizeof(addr_));
    addr_.sin_family = AF_INET;
    addr_.sin_port = htons(port);
    addr_.sin_addr.s_addr = htonl(loopbackOnly ? INADDR_LOOPBACK : INADDR_ANY);
}

Address::Address(const std::string& ip, uint16_t port) {
    std::memset(&addr_, 0, sizeof(addr_));
    addr_.sin_family = AF_INET;
    addr_.sin_port = htons(port);
    inet_pton(AF_INET, ip.c_str(), &addr_.sin_addr);
}

Address::Address(const struct sockaddr_in& addr) : addr_(addr) {}

std::string Address::toIp() const {
    char buf[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &addr_.sin_addr, buf, sizeof(buf));
    return std::string(buf);
}

std::string Address::toIpPort() const {
    return toIp() + ":" + std::to_string(port());
}

uint16_t Address::port() const {
    return ntohs(addr_.sin_port);
}

sa_family_t Address::family() const {
    return addr_.sin_family;
}

const struct sockaddr* Address::getSockAddr() const {
    return reinterpret_cast<const struct sockaddr*>(&addr_);
}

struct sockaddr_in Address::toSockAddr() const {
    return addr_;
}

uint32_t Address::ipv4NetEndian() const {
    return addr_.sin_addr.s_addr;
}

uint16_t Address::portNetEndian() const {
    return addr_.sin_port;
}

} // namespace network
} // namespace platform
} // namespace zexuan

#endif // ZEXUAN_WINDOWS
