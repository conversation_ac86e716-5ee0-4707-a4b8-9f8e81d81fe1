/**
 * @file message.cpp
 * @brief Message structure implementation based on IEC 60870-5-103 protocol
 * <AUTHOR> project
 * @date 2024
 */

#include "zexuan/base/message.hpp"
#include <fstream>
#include <sstream>
#include <iomanip>

namespace zexuan {
namespace base {

Message::Message() 
    : lengthL_(0), lengthH_(0), typ_(0), vsq_(0), cot_(0), 
      source_(0), target_(0), fun_(0), inf_(0) {
    updateLengthBytes();
}

Message::Message(uint8_t typ, uint8_t vsq, uint8_t cot, uint8_t source,
                uint8_t fun, uint8_t inf)
    : lengthL_(0), lengthH_(0), typ_(typ), vsq_(vsq), cot_(cot),
      source_(source), target_(0), fun_(fun), inf_(inf) {
    updateLengthBytes();
}

size_t Message::getMessageSize() const {
    // START(1) + LENGTH_L(1) + LENGTH_H(1) + ASDU data
    return 3 + getAsduSize();
}

size_t Message::getAsduSize() const {
    // TYP(1) + VSQ(1) + COT(1) + SOURCE(1) + TARGET(1) + FUN(1) + INF(1) + Variable Structure
    return 7 + variableStructure_.size();
}

void Message::updateLengthBytes() {
    size_t asduSize = getAsduSize();
    lengthL_ = static_cast<uint8_t>(asduSize & 0xFF);
    lengthH_ = static_cast<uint8_t>((asduSize >> 8) & 0xFF);
}

size_t Message::serialize(std::vector<uint8_t>& buffer) const {
    buffer.clear();
    
    // START character
    buffer.push_back(START_CHAR);
    
    // Length bytes
    buffer.push_back(lengthL_);
    buffer.push_back(lengthH_);
    
    // ASDU fields
    buffer.push_back(typ_);
    buffer.push_back(vsq_);
    buffer.push_back(cot_);
    buffer.push_back(source_);
    buffer.push_back(target_);
    buffer.push_back(fun_);
    buffer.push_back(inf_);
    
    // Variable structure
    buffer.insert(buffer.end(), variableStructure_.begin(), variableStructure_.end());
    
    return buffer.size();
}

size_t Message::deserialize(const std::vector<uint8_t>& buffer, size_t offset) {
    if (buffer.size() < offset + 3) {
        return 0; // Not enough data for header
    }
    
    size_t pos = offset;
    
    // Check START character
    if (buffer[pos] != START_CHAR) {
        return 0; // Invalid start character
    }
    pos++;
    
    // Read length bytes
    lengthL_ = buffer[pos++];
    lengthH_ = buffer[pos++];
    
    size_t asduSize = lengthL_ | (static_cast<size_t>(lengthH_) << 8);
    
    // Check if we have enough data for the complete ASDU
    if (buffer.size() < pos + asduSize) {
        return 0; // Not enough data
    }
    
    // Check minimum ASDU size (7 bytes for fixed fields)
    if (asduSize < 7) {
        return 0; // Invalid ASDU size
    }
    
    // Read ASDU fields
    typ_ = buffer[pos++];
    vsq_ = buffer[pos++];
    cot_ = buffer[pos++];
    source_ = buffer[pos++];
    target_ = buffer[pos++];
    fun_ = buffer[pos++];
    inf_ = buffer[pos++];
    
    // Read variable structure
    size_t variableSize = asduSize - 7;
    variableStructure_.clear();
    if (variableSize > 0) {
        variableStructure_.assign(buffer.begin() + pos, buffer.begin() + pos + variableSize);
        pos += variableSize;
    }
    
    return pos - offset; // Return number of bytes consumed
}

bool Message::loadFromHexFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return false;
    }
    
    std::string line;
    std::vector<uint8_t> buffer;
    
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string hexByte;
        
        while (iss >> hexByte) {
            try {
                uint8_t byte = static_cast<uint8_t>(std::stoul(hexByte, nullptr, 16));
                buffer.push_back(byte);
            } catch (const std::exception&) {
                // Skip invalid hex values
                continue;
            }
        }
    }
    
    if (buffer.empty()) {
        return false;
    }
    
    return deserialize(buffer) > 0;
}

} // namespace base
} // namespace zexuan
