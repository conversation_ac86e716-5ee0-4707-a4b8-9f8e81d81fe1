/**
 * @file subject.cpp
 * @brief 目标者类实现 - 现代化版本
 * <AUTHOR> from CNXSubject
 * @date 2025-08-16
 */

#include "zexuan/base/subject.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/utils/invoke_id_utils.hpp"
#include <iostream>
#include <format>

namespace zexuan {
namespace base {

Subject::Subject(ObjectId object_id, std::shared_ptr<Mediator> mediator)
    : RegisterObject(object_id, "Subject") {
    // 设置基类的 Mediator
    SetMediator(mediator);

    // 默认设置为管理设备为空
    reg_obj_.devices = std::nullopt;

    // 默认设置关注的事件信息为空(目标者一般不关注事件信息)
    reg_obj_.event_types = std::nullopt;
}

Subject::~Subject() noexcept {
    // 基类析构函数会调用Exit()
}

VoidResult Subject::SetCareDevices(std::optional<DeviceList> devices) {
    try {
        std::lock_guard<std::mutex> lock(state_mutex_);
        reg_obj_.devices = std::move(devices);
        return {};
    } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
    }
}

bool Subject::IsCareDevInfo(const DeviceUUID& device_id) const noexcept {
    return RegisterObject::IsCareDevInfo(device_id);
}

VoidResult Subject::SetManagerDev(DeviceList devices) {
    // 向后兼容的接口，转发到新的接口
    return SetCareDevices(std::move(devices));
}

Result<void> Subject::PushCommand(const CommonMessage& command_msg, ObjectId source_id) {
    std::lock_guard<std::mutex> lock(callback_mutex_);

    if (!cmd_callback_) {
        std::cerr << std::format("PushCommand() 回调函数未设置，无法处理收到的命令\n");
        return std::unexpected(ErrorCode::CALLBACK_NOT_SET);
    }

    std::cout << std::format("PushCommand() 收到来自对象(ID={})发送的命令\n", source_id);

    // 调用回调函数处理命令
    auto result = cmd_callback_.value()(command_msg);
    return result.transform([]() { return; }); // 转换为 Result<void>
}

Result<void> Subject::SendResult(const CommonMessage& result_msg) {
    auto mediator = GetMediator();
    if (!mediator) {
        std::cerr << "SendResult() 服务中介对象为空\n";
        return std::unexpected(ErrorCode::MEDIATOR_NOT_AVAILABLE);
    }

    // 自动从 invoke_id 中解析出原始 Observer 的 ID
    ObjectId observer_id = utils::invoke_id::ParseObjectId(result_msg.invoke_id);
    if (observer_id == INVALID_ID) {
        std::cerr << std::format("SendResult() 无法从 invoke_id '{}' 中解析出观察者ID\n", result_msg.invoke_id);
        return std::unexpected(ErrorCode::INVALID_PARAMETER);
    }

    auto result = mediator->SendCommonMsgToObserver(observer_id, result_msg, reg_obj_.object_id);

    if (!result) {
        std::cerr << std::format("SendResult() 发送结果失败\n");
        return result;
    } else {
        std::cout << std::format("SendResult() 自动发送结果给观察者ID={}\n", observer_id);
        return {};
    }
}

Result<void> Subject::SendEventNotify(const EventMessage& event_msg) {
    auto mediator = GetMediator();
    if (!mediator) {
        std::cerr << "SendEventNotify() 服务中介对象为空\n";
        return std::unexpected(ErrorCode::MEDIATOR_NOT_AVAILABLE);
    }

    auto result = mediator->SendEventMsgToObserver(event_msg, reg_obj_.object_id);

    if (!result) {
        std::cerr << std::format("SendEventNotify() 发送事件通知失败\n");
        return std::unexpected(result.error());
    } else {
        std::cout << std::format("SendEventNotify() 发送事件通知成功，通知了{}个观察者\n", result.value());
        return {};
    }
}

VoidResult Subject::SetCmdCallback(CommonMessageHandler callback_func) noexcept {
    try {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        cmd_callback_ = std::move(callback_func);
        return {};
    } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
    }
}

bool Subject::IsCare(EventType event_type, const DeviceUUID& device_id) const noexcept {
    // 目标者主要关注设备，事件类型一般不关注
    return IsCareDevInfo(device_id);
}

VoidResult Subject::DoRegister() {
    auto mediator = GetMediator();
    if (!mediator) {
        return std::unexpected(ErrorCode::MEDIATOR_NOT_AVAILABLE);
    }

    auto result = mediator->EnrollSubject(reg_obj_.object_id,
                                         std::static_pointer_cast<Subject>(shared_from_this()));

    if (!result) {
        std::cerr << std::format("DoRegister() 注册目标者到中介者失败\n");
        return result;
    }

    return {};
}

VoidResult Subject::DoUnregister() noexcept {
    try {
        auto mediator = GetMediator();
        if (!mediator) {
            return {};
        }

        auto result = mediator->CancelSubject(reg_obj_.object_id);

        if (!result) {
            std::cerr << std::format("DoUnregister() 从中介者注销目标者失败\n");
            return result;
        }

        return {};
    } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
    }
}

} // namespace base
} // namespace zexuan
