#include "zexuan/net/event_loop_thread.hpp"
#include "zexuan/net/event_loop.hpp"
#include <cassert>
#include <spdlog/spdlog.h>

using namespace zexuan;
using namespace zexuan::net;

EventLoopThread::EventLoopThread(const ThreadInitCallback& cb, const std::string& name)
    : loop_(NULL),
      exiting_(false),
      mutex_(),
      cond_(),
      callback_(cb),
      name_(name) {
}

EventLoopThread::~EventLoopThread() {
    exiting_ = true;
    if (loop_ != NULL) // not 100% race-free, eg. threadFunc could be running callback_.
    {
        // still a tiny chance to call destructed object, if thread<PERSON><PERSON><PERSON> exits just now.
        // but when EventLoopThread destructs, usually programming is exiting anyway.
        loop_->quit();
        thread_.join();
    }
}

EventLoop* EventLoopThread::startLoop() {
    assert(!thread_.joinable());
    thread_ = std::thread(&EventLoopThread::threadFunc, this);

    EventLoop* loop = NULL;
    {
        std::unique_lock<std::mutex> lock(mutex_);
        while (loop_ == NULL) {
            cond_.wait(lock);
        }
        loop = loop_;
    }

    return loop;
}

void EventLoopThread::threadFunc() {
    EventLoop loop;

    if (callback_) {
        callback_(&loop);
    }

    {
        std::lock_guard<std::mutex> lock(mutex_);
        loop_ = &loop;
        cond_.notify_one();
    }

    loop.loop();
    //assert(exiting_);
    std::lock_guard<std::mutex> lock(mutex_);
    loop_ = NULL;
}

