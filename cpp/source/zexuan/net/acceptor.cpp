#include "zexuan/net/acceptor.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/platform/network/sockets_ops.hpp"
#include <fcntl.h>
#include <unistd.h>
#include <cassert>
#include <spdlog/spdlog.h>

using namespace zexuan;
using namespace zexuan::net;
using namespace zexuan::platform::network;

Acceptor::Acceptor(EventLoop* loop, const Address& listenAddr, bool reuseport)
    : loop_(loop),
      acceptSocket_(zexuan::platform::network::sockets::createNonblockingOrDie(listenAddr.family())),
      acceptChannel_(loop, acceptSocket_.fd()),
      listening_(false),
      idleFd_(::open("/dev/null", O_RDONLY | O_CLOEXEC)) {
    assert(idleFd_ >= 0);
    acceptSocket_.setReuseAddr(true);
    acceptSocket_.setReusePort(reuseport);
    acceptSocket_.bindAddress(listenAddr);
    acceptChannel_.setReadCallback([this](std::chrono::system_clock::time_point) { handleRead(std::chrono::system_clock::now()); });
}

Acceptor::~Acceptor() {
    acceptChannel_.disableAll();
    acceptChannel_.remove();
    ::close(idleFd_);
}

void Acceptor::listen() {
    loop_->assertInLoopThread();
    listening_ = true;
    acceptSocket_.listen();
    acceptChannel_.enableReading();
}

void Acceptor::handleRead(std::chrono::system_clock::time_point) {
    loop_->assertInLoopThread();
    Address peerAddr;
    //FIXME loop until no more
    int connfd = acceptSocket_.accept(&peerAddr);
    if (connfd >= 0) {
        // string hostport = peerAddr.toIpPort();
        // LOG_TRACE << "Accepts of " << hostport;
        if (newConnectionCallback_) {
            newConnectionCallback_(connfd, peerAddr);
        } else {
            zexuan::platform::network::sockets::close(connfd);
        }
    } else {
        spdlog::error("in Acceptor::handleRead");
        // Read the section named "The special problem of
        // accept()ing when you can't" in libev's doc.
        // By Marc Lehmann, author of livev.
        if (errno == EMFILE) {
            ::close(idleFd_);
            idleFd_ = ::accept(acceptSocket_.fd(), NULL, NULL);
            ::close(idleFd_);
            idleFd_ = ::open("/dev/null", O_RDONLY | O_CLOEXEC);
        }
    }
}


