#include "zexuan/net/tcp_connection.hpp"
#include "zexuan/net/channel.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/platform/network/socket.hpp"
#include "zexuan/platform/network/sockets_ops.hpp"
#include <netinet/tcp.h>
#include <sstream>
#include <spdlog/spdlog.h>

using namespace zexuan;
using namespace zexuan::net;
using namespace zexuan::platform::network;

void zexuan::net::defaultConnectionCallback(const TcpConnectionPtr& conn) {
    spdlog::info("{} -> {} is {}", 
                 conn->localAddress().toIpPort(),
                 conn->peerAddress().toIpPort(),
                 (conn->connected() ? "UP" : "DOWN"));
}

void zexuan::net::defaultMessageCallback(const TcpConnectionPtr& conn,
                                         Buffer* buffer,
                                         std::chrono::system_clock::time_point receiveTime) {
    spdlog::info("{} -> {} received {} bytes at {}", 
                 conn->localAddress().toIpPort(),
                 conn->peerAddress().toIpPort(),
                 buffer->readableBytes(),
                 std::chrono::duration_cast<std::chrono::microseconds>(
                     receiveTime.time_since_epoch()).count());

    buffer->retrieveAll();
}

TcpConnection::TcpConnection(EventLoop* loop,
                             const std::string& nameArg,
                             int sockfd,
                             const Address& localAddr,
                             const Address& peerAddr)
    : loop_(loop),
      name_(nameArg),
      state_(kConnecting),
      reading_(true),
      socket_(new zexuan::platform::network::Socket(sockfd)),
      channel_(new Channel(loop, sockfd)),
      localAddr_(localAddr),
      peerAddr_(peerAddr),
      highWaterMark_(64*1024*1024) {
    channel_->setReadCallback(
        [this](std::chrono::system_clock::time_point receiveTime) { handleRead(receiveTime); });
    channel_->setWriteCallback([this]() { handleWrite(); });
    channel_->setCloseCallback([this]() { handleClose(); });
    channel_->setErrorCallback([this]() { handleError(); });
    spdlog::debug("TcpConnection::ctor[{}] at {} fd={}", name_, static_cast<void*>(this), sockfd);

    socket_->setKeepAlive(true);
}

TcpConnection::~TcpConnection() {
    spdlog::debug("TcpConnection::dtor[{}] at {} fd={} state={}", 
                  name_, static_cast<void*>(this), channel_->fd(), stateToString());
}

bool TcpConnection::getTcpInfo(struct tcp_info* tcpi) const {
    return socket_->getTcpInfo(tcpi);
}

std::string TcpConnection::getTcpInfoString() const {
    char buf[1024];
    buf[0] = '\0';
    socket_->getTcpInfoString(buf, sizeof buf);
    return buf;
}

void TcpConnection::send(const void* data, int len) {
    send(std::string_view(static_cast<const char*>(data), len));
}

void TcpConnection::send(const std::string_view& message) {
    if (state_ == kConnected) {
        if (loop_->isInLoopThread()) {
            sendInLoop(message);
        } else {
            loop_->runInLoop([this, message = std::string(message)]() {
                sendInLoop(message);
            });
        }
    }
}

// FIXME efficiency!!!
void TcpConnection::send(Buffer* buf) {
    if (state_ == kConnected) {
        if (loop_->isInLoopThread()) {
            sendInLoop(buf->peek(), buf->readableBytes());
            buf->retrieveAll();
        } else {
            loop_->runInLoop([this, buf]() {
                sendInLoop(buf->peek(), buf->readableBytes());
                buf->retrieveAll();
            });
        }
    }
}

void TcpConnection::sendInLoop(const void* data, size_t len) {
    loop_->assertInLoopThread();
    ssize_t nwrote = 0;
    size_t remaining = len;
    bool faultError = false;
    if (state_ == kDisconnected) {
        spdlog::warn("disconnected, give up writing");
        return;
    }

    // if no thing in output queue, try writing directly
    if (!channel_->isWriting() && outputBuffer_.readableBytes() == 0) {
        nwrote = zexuan::platform::network::sockets::write(channel_->fd(), data, len);
        if (nwrote >= 0) {
            remaining = len - nwrote;
            if (remaining == 0 && writeCompleteCallback_) {
                loop_->queueInLoop([this]() { writeCompleteCallback_(shared_from_this()); });
            }
        } else // nwrote < 0
        {
            nwrote = 0;
            if (errno != EWOULDBLOCK) {
                spdlog::error("TcpConnection::sendInLoop");
                if (errno == EPIPE || errno == ECONNRESET) // FIXME: any others?
                {
                    faultError = true;
                }
            }
        }
    }

    assert(remaining <= len);
    if (!faultError && remaining > 0) {
        size_t oldLen = outputBuffer_.readableBytes();
        if (oldLen + remaining >= highWaterMark_
            && oldLen < highWaterMark_
            && highWaterMarkCallback_) {
            loop_->queueInLoop([this, oldLen, remaining]() {
                highWaterMarkCallback_(shared_from_this(), oldLen + remaining);
            });
        }

        outputBuffer_.append(static_cast<const char*>(data)+nwrote, remaining);
        if (!channel_->isWriting()) {
            channel_->enableWriting();
        }
    }
}

void TcpConnection::sendInLoop(const std::string_view& message) {
    sendInLoop(message.data(), message.size());
}

void TcpConnection::shutdown() {
    // FIXME: use compare and swap
    if (state_ == kConnected) {
        setState(kDisconnecting);
        // FIXME: shared_from_this()?
        loop_->runInLoop([this]() { shutdownInLoop(); });
    }
}

void TcpConnection::shutdownInLoop() {
    loop_->assertInLoopThread();
    if (!channel_->isWriting()) {
        // we are not writing
        socket_->shutdownWrite();
    }
}

void TcpConnection::forceClose() {
    // FIXME: use compare and swap
    if (state_ == kConnected || state_ == kDisconnecting) {
        setState(kDisconnecting);
        loop_->queueInLoop([this]() { forceCloseInLoop(); });
    }
}

void TcpConnection::forceCloseWithDelay(double seconds) {
    if (state_ == kConnected || state_ == kDisconnecting) {
        setState(kDisconnecting);
        loop_->runInLoop([this, seconds]() {
            // FIXME: implement delay close
            forceCloseInLoop();
        });
    }
}

void TcpConnection::forceCloseInLoop() {
    loop_->assertInLoopThread();
    if (state_ == kConnected || state_ == kDisconnecting) {
        // as if we received 0 byte in handleRead();
        handleClose();
    }
}

const char* TcpConnection::stateToString() const {
    switch (state_) {
        case kDisconnected:
            return "kDisconnected";
        case kConnecting:
            return "kConnecting";
        case kConnected:
            return "kConnected";
        case kDisconnecting:
            return "kDisconnecting";
        default:
            return "unknown state";
    }
}

void TcpConnection::setTcpNoDelay(bool on) {
    socket_->setTcpNoDelay(on);
}

void TcpConnection::startRead() {
    loop_->runInLoop([this]() { startReadInLoop(); });
}

void TcpConnection::startReadInLoop() {
    loop_->assertInLoopThread();
    if (!reading_ || !channel_->isReading()) {
        channel_->enableReading();
        reading_ = true;
    }
}

void TcpConnection::stopRead() {
    loop_->runInLoop([this]() { stopReadInLoop(); });
}

void TcpConnection::stopReadInLoop() {
    loop_->assertInLoopThread();
    if (reading_ || channel_->isReading()) {
        channel_->disableReading();
        reading_ = false;
    }
}

void TcpConnection::connectEstablished() {
    loop_->assertInLoopThread();
    assert(state_ == kConnecting);
    setState(kConnected);
    channel_->tie(shared_from_this());
    channel_->enableReading();

    connectionCallback_(shared_from_this());
}

void TcpConnection::connectDestroyed() {
    loop_->assertInLoopThread();
    if (state_ == kConnected) {
        setState(kDisconnected);
        channel_->disableAll();

        connectionCallback_(shared_from_this());
    }

    channel_->remove();
}

void TcpConnection::handleRead(std::chrono::system_clock::time_point receiveTime) {
    loop_->assertInLoopThread();
    int savedErrno = 0;
    ssize_t n = inputBuffer_.readFd(channel_->fd(), &savedErrno);
    if (n > 0) {
        messageCallback_(shared_from_this(), &inputBuffer_, receiveTime);
    } else if (n == 0) {
        handleClose();
    } else {
        errno = savedErrno;
        spdlog::error("TcpConnection::handleRead");
        handleError();
    }
}

void TcpConnection::handleWrite() {
    loop_->assertInLoopThread();
    if (channel_->isWriting()) {
        ssize_t n = zexuan::platform::network::sockets::write(channel_->fd(),
                                   outputBuffer_.peek(),
                                   outputBuffer_.readableBytes());
        if (n > 0) {
            outputBuffer_.retrieve(n);
            if (outputBuffer_.readableBytes() == 0) {
                channel_->disableWriting();
                if (writeCompleteCallback_) {
                    loop_->queueInLoop([this]() { writeCompleteCallback_(shared_from_this()); });
                }

                if (state_ == kDisconnecting) {
                    shutdownInLoop();
                }
            }
        } else {
            spdlog::error("TcpConnection::handleWrite");
        }
    } else {
        spdlog::trace("Connection fd = {} is down, no more writing", channel_->fd());
    }
}

void TcpConnection::handleClose() {
    loop_->assertInLoopThread();
    spdlog::trace("fd = {} state = {}", channel_->fd(), stateToString());

    assert(state_ == kConnected || state_ == kDisconnecting);
    // we don't close fd, leave it to dtor, so we can find leaks easily.
    setState(kDisconnected);
    channel_->disableAll();

    TcpConnectionPtr guardThis(shared_from_this());
    connectionCallback_(guardThis);
    // must be the last line
    closeCallback_(guardThis);
}

void TcpConnection::handleError() {
    int err = zexuan::platform::network::sockets::getSocketError(channel_->fd());
    spdlog::error("TcpConnection::handleError [{}] - SO_ERROR = {} {}", 
                  name_, err, strerror(err));
}

