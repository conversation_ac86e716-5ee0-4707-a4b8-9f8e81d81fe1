#include "zexuan/protocol/transform/gw104/asdu_type4_gws.hpp"
#include "zexuan/base/message.hpp"
#include <spdlog/spdlog.h>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>

namespace zexuan {
namespace protocol {
namespace transform {
namespace gw104 {

int AsduType4GWS::DirectResponseFromLocal(const base::ProtocolFrame& request_frame,
                                         base::ProtocolFrameList& response_frames) {
    spdlog::info("ASDU Type4: Processing direct local response request, frame_id={}, data_size={}",
                request_frame.frame_id, request_frame.data.size());

    try {
        // 创建符合 IEC 60870-5-103 协议格式的响应消息
        base::Message response_message;

        // 设置消息头部字段
        response_message.setTyp(GetSupportedType());    // TYP: ASDU类型标识 = 4
        response_message.setVsq(0x01);                  // VSQ: 可变结构限定词，单个信息元素
        response_message.setCot(0x07);                  // COT: 传送原因，激活确认
        response_message.setSource(0x00);               // 源地址，本地设备
        response_message.setTarget(static_cast<uint8_t>(request_frame.asdu_addr & 0xFF)); // 目标地址，来自请求
        response_message.setFun(0xF1);                  // FUN: 功能类型，本地状态响应
        response_message.setInf(0x01);                  // INF: 信息序号，状态信息

        // 构建可变结构体数据
        std::vector<uint8_t> variable_data;

        // 添加当前时间戳（7字节 IEC 时间格式）
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::tm* local_time = std::localtime(&time_t);

        // IEC 60870-5-103 时间格式（7字节）
        uint16_t milliseconds = static_cast<uint16_t>(ms.count());
        variable_data.push_back(static_cast<uint8_t>(milliseconds & 0xFF));        // 毫秒低字节
        variable_data.push_back(static_cast<uint8_t>((milliseconds >> 8) & 0xFF)); // 毫秒高字节
        variable_data.push_back(static_cast<uint8_t>(local_time->tm_min));         // 分钟
        variable_data.push_back(static_cast<uint8_t>(local_time->tm_hour));        // 小时
        variable_data.push_back(static_cast<uint8_t>(local_time->tm_mday));        // 日
        variable_data.push_back(static_cast<uint8_t>(local_time->tm_mon + 1));     // 月
        variable_data.push_back(static_cast<uint8_t>((local_time->tm_year + 1900) - 2000)); // 年

        // 添加本地状态数据
        variable_data.push_back(0x01); // 设备状态：在线
        variable_data.push_back(0x02); // 通信状态：正常
        variable_data.push_back(0x03); // 运行模式：自动
        variable_data.push_back(0x00); // 告警状态：无告警

        // 添加负载状态（4字节）
        uint32_t load_percent = 75; // 75% 负载
        variable_data.push_back(static_cast<uint8_t>(load_percent & 0xFF));
        variable_data.push_back(static_cast<uint8_t>((load_percent >> 8) & 0xFF));
        variable_data.push_back(static_cast<uint8_t>((load_percent >> 16) & 0xFF));
        variable_data.push_back(static_cast<uint8_t>((load_percent >> 24) & 0xFF));

        // 设置可变结构体数据
        response_message.setVariableStructure(variable_data);

        // 创建响应帧并序列化消息
        base::ProtocolFrame response_frame;
        response_frame.data.clear();
        size_t serialized_size = response_message.serialize(response_frame.data);

        // 设置协议帧属性
        response_frame.type = GetSupportedType();
        response_frame.frame_id = request_frame.frame_id;
        response_frame.asdu_addr = request_frame.asdu_addr;
        response_frame.cot = 0x07; // 激活确认

        // 添加到响应列表
        response_frames.push_back(response_frame);

        spdlog::info("ASDU Type4: Successfully generated local response using Message::serialize(), "
                    "serialized_size={}, load={}%, timestamp=20{:02d}-{:02d}-{:02d} {:02d}:{:02d}.{:03d}",
                    serialized_size, load_percent,
                    (local_time->tm_year + 1900) - 2000, local_time->tm_mon + 1, local_time->tm_mday,
                    local_time->tm_hour, local_time->tm_min, milliseconds);

        return 0; // 成功

    } catch (const std::exception& e) {
        spdlog::error("ASDU Type4: Exception in DirectResponseFromLocal: {}", e.what());
        return -1; // 失败
    }
}

// 注意：原来的私有方法已经不再需要，因为我们现在直接在 DirectResponseFromLocal 中
// 使用 Message 类来构建符合 IEC 60870-5-103 协议格式的数据

} // namespace gw104
} // namespace transform
} // namespace protocol
} // namespace zexuan