# ---- Development Tools Configuration ----

# Only activate tools for top level project
if(NOT PROJECT_SOURCE_DIR STREQUAL CMAKE_SOURCE_DIR)
  return()
endif()

include(${CMAKE_CURRENT_LIST_DIR}/CPM.cmake)

# ---- Sanitizers ----
# Usage: cmake -DUSE_SANITIZER=Address
if(USE_SANITIZER)
  CPMAddPackage("gh:StableCoder/cmake-scripts#24.04")
  include(${cmake-scripts_SOURCE_DIR}/sanitizers.cmake)
endif()

# ---- Static Analysis ----
# Usage: cmake -DUSE_STATIC_ANALYZER="clang-tidy;cppcheck"
if(USE_STATIC_ANALYZER)
  CPMAddPackage("gh:StableCoder/cmake-scripts#24.04")
  
  if("clang-tidy" IN_LIST USE_STATIC_ANALYZER)
    set(CLANG_TIDY ON CACHE INTERNAL "")
  endif()
  
  if("cppcheck" IN_LIST USE_STATIC_ANALYZER)
    set(CPPCHECK ON CACHE INTERNAL "")
  endif()
  
  include(${cmake-scripts_SOURCE_DIR}/tools.cmake)
  
  if(CLANG_TIDY)
    clang_tidy(${CLANG_TIDY_ARGS})
  endif()
  
  if(CPPCHECK)
    cppcheck(${CPPCHECK_ARGS})
  endif()
endif()

# ---- Code Coverage ----
# Usage: cmake -DENABLE_COVERAGE=ON
if(ENABLE_COVERAGE)
  if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(-O0 -g --coverage)
    add_link_options(--coverage)
  endif()
endif()

# ---- Ccache ----
# Usage: cmake -DUSE_CCACHE=ON
if(USE_CCACHE)
  find_program(CCACHE_PROGRAM ccache)
  if(CCACHE_PROGRAM)
    set(CMAKE_CXX_COMPILER_LAUNCHER "${CCACHE_PROGRAM}")
    message(STATUS "Using ccache: ${CCACHE_PROGRAM}")
  else()
    message(WARNING "ccache requested but not found")
  endif()
endif()
