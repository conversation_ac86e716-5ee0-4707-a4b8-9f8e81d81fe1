/**
 * @file interfaces.hpp
 * @brief 接口定义 - 遵循接口隔离原则
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_INTERFACES_HPP
#define ZEXUAN_BASE_INTERFACES_HPP

#include "type_aliases.hpp"
#include <memory>
#include <optional>

namespace zexuan {
namespace base {

// ============================================================================
// 接口定义 - 遵循接口隔离原则
// ============================================================================

/// @brief 可注册接口 - 定义对象注册到中介者的能力
class IRegisterable {
public:
    virtual ~IRegisterable() = default;

    /// @brief 将自身注册到服务中介
    virtual VoidResult RegisterToMediator() = 0;

    /// @brief 将自身从服务中介注销
    virtual VoidResult CancelFromMediator() noexcept = 0;

    /// @brief 获取对象ID
    virtual ObjectId GetObjectId() const noexcept = 0;
};

/// @brief 事件感知接口 - 定义对事件的关注能力
class IEventAware {
public:
    virtual ~IEventAware() = default;

    /// @brief 设置关注的事件类型
    virtual VoidResult SetCareEventType(std::optional<EventTypeList> event_types) = 0;

    /// @brief 判断是否关注指定事件类型
    virtual bool IsCareEventInfo(EventType event_type) const noexcept = 0;
};

/// @brief 设备感知接口 - 定义对设备的关注能力
class IDeviceAware {
public:
    virtual ~IDeviceAware() = default;

    /// @brief 设置关注的设备列表
    virtual VoidResult SetCareDevices(std::optional<DeviceList> devices) = 0;

    /// @brief 判断是否关注指定设备
    virtual bool IsCareDevInfo(const DeviceUUID& device_id) const noexcept = 0;
};

/// @brief 中介者服务接口 - 定义中介者的核心服务
class IMediatorService {
public:
    virtual ~IMediatorService() = default;

    /// @brief 注册对象到中介者
    virtual VoidResult RegisterObject(std::shared_ptr<IRegisterable> object) = 0;

    /// @brief 从中介者注销对象
    virtual VoidResult UnregisterObject(ObjectId object_id) noexcept = 0;
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_INTERFACES_HPP
