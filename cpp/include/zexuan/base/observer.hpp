/**
 * @file observer.h
 * @brief 观察者类定义
 * <AUTHOR> from CNXObserver
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_OBSERVER_H
#define ZEXUAN_BASE_OBSERVER_H

#include "register_object.hpp"

namespace zexuan {
namespace base {

/**
 * @brief 观察者对象
 *
 * 向装置目标者发送请求获取相关信息，接收目标者推送的信息并实时处理
 * 使用现代 C++ 特性实现线程安全和异常安全
 * 重构后实现IEventAware和IDeviceAware接口，提供更好的事件和设备管理功能
 */
class Observer : public RegisterObject, public IEventAware, public IDeviceAware {
public:
    /**
     * @brief 构造函数
     * @param object_id 对象唯一标识
     * @param mediator 中介者实例
     */
    Observer(ObjectId object_id, std::shared_ptr<Mediator> mediator);

    /**
     * @brief 析构函数
     */
    ~Observer() noexcept override;

    /**
     * @brief 设置关注的事件信息类型 (实现IEventAware接口)
     * @param event_types 事件类型列表，nullopt 表示不关注任何事件
     * @return 操作结果
     */
    VoidResult SetCareEventType(std::optional<EventTypeList> event_types) override;

    /**
     * @brief 判断是否关注指定事件类型 (实现IEventAware接口)
     * @param event_type 事件类型
     * @return true:关注 false:不关注
     */
    bool IsCareEventInfo(EventType event_type) const noexcept override;

    /**
     * @brief 设置关注的设备 (实现IDeviceAware接口)
     * @param devices 设备列表，nullopt 表示不关注任何设备
     * @return 操作结果
     */
    VoidResult SetCareDevices(std::optional<DeviceList> devices) override;

    /**
     * @brief 判断是否关注指定设备 (实现IDeviceAware接口)
     * @param device_id 设备ID
     * @return true:关注 false:不关注
     */
    bool IsCareDevInfo(const DeviceUUID& device_id) const noexcept override;

    /**
     * @brief 设置关注的设备 (向后兼容的接口)
     * @param devices 设备列表，nullopt 表示不关注任何设备
     * @return 操作结果
     */
    virtual VoidResult SetCareDev(std::optional<DeviceList> devices);

    /**
     * @brief 发送命令给目标者，观察方有请求需要发送时调用
     * @param command_msg 命令信息
     * @param subject_id 目标者ID，如果为INVALID_ID，则通过管理的设备找到目标者ID后发送
     * @return 操作结果
     */
    virtual Result<void> SendCommand(const CommonMessage& command_msg, ObjectId subject_id = INVALID_ID);

    /**
     * @brief 回复结果，目标者方有结果准备就绪时通过服务中介接口触发调用
     * @param result_msg 结果信息
     * @param source_id 发送方(目标者)的唯一标识
     * @return 操作结果
     */
    virtual Result<void> ReplyResult(const CommonMessage& result_msg, ObjectId source_id);

    /**
     * @brief 目标者推送事件通知，目标者方有事件通知时通过服务中介接口触发调用
     * @param event_msg 事件信息
     * @param source_id 发送方(目标者方)的唯一标识
     * @return 操作结果
     */
    virtual Result<void> PushEventNotify(const EventMessage& event_msg, ObjectId source_id);

    /**
     * @brief 设置收到目标者结果的回调处理函数
     * @param callback_func 现代化回调函数
     * @return 操作结果
     */
    virtual VoidResult SetResultCallback(CommonMessageHandler callback_func) noexcept;

    /**
     * @brief 设置收到目标者事件的回调处理函数
     * @param callback_func 现代化回调函数
     * @return 操作结果
     */
    virtual VoidResult SetEventCallback(EventMessageHandler callback_func) noexcept;

    /**
     * @brief 判断对于参数指定的事件类型和设备是否关注
     * @param event_type 事件类型
     * @param device_id 设备ID
     * @return true:关注 false:不关注
     */
    bool IsCare(EventType event_type, const DeviceUUID& device_id) const noexcept override;

protected:
    /**
     * @brief 派生类特定的注册逻辑 (重写基类钩子方法)
     * @return 操作结果
     */
    VoidResult DoRegister() override;

    /**
     * @brief 派生类特定的注销逻辑 (重写基类钩子方法)
     * @return 操作结果
     */
    VoidResult DoUnregister() noexcept override;

private:
    /** @brief 结果处理回调函数 */
    std::optional<CommonMessageHandler> result_callback_;

    /** @brief 事件处理回调函数 */
    std::optional<EventMessageHandler> event_callback_;

    /** @brief 保护回调函数的互斥锁 */
    mutable std::mutex callback_mutex_;
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_OBSERVER_H
