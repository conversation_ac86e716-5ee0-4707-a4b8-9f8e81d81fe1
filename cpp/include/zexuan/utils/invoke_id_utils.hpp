/**
 * @file invoke_id_utils.h
 * @brief invoke_id 工具函数定义 - 使用跨平台 UUID
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#ifndef ZEXUAN_UTILS_INVOKE_ID_UTILS_H
#define ZEXUAN_UTILS_INVOKE_ID_UTILS_H

#include <string>

namespace zexuan {
namespace utils {

/// @brief invoke_id 工具函数命名空间
namespace invoke_id {

    /// @brief 生成 invoke_id
    /// @param object_id 对象ID
    /// @param sequence 序列号（可选，为空时自动生成UUID）
    /// @return 格式化的 invoke_id (格式: 对象ID#ZX#序列号)
    std::string Generate(int object_id, const std::string& sequence = "");

    /// @brief 从 invoke_id 中解析对象ID
    /// @param invoke_id 调用ID
    /// @return 对象ID，失败返回 -1
    int ParseObjectId(const std::string& invoke_id);

    /// @brief 从 invoke_id 中解析序列号
    /// @param invoke_id 调用ID
    /// @return 序列号
    std::string ParseSequence(const std::string& invoke_id);

    /// @brief 验证 invoke_id 格式是否正确
    /// @param invoke_id 调用ID
    /// @return true-格式正确 false-格式错误
    bool IsValid(const std::string& invoke_id);

} // namespace invoke_id
} // namespace utils
} // namespace zexuan

#endif // ZEXUAN_UTILS_INVOKE_ID_UTILS_H
