#ifndef ZEXUAN_NET_ACCEPTOR_HPP
#define ZEXUAN_NET_ACCEPTOR_HPP

#include <functional>

#include "zexuan/net/channel.hpp"
#include "zexuan/platform/network/socket.hpp"

namespace zexuan {
namespace net {

class EventLoop;

///
/// Acceptor of incoming TCP connections.
///
class Acceptor {
public:
    typedef std::function<void (int sockfd, const zexuan::platform::network::Address&)> NewConnectionCallback;

    Acceptor(EventLoop* loop, const zexuan::platform::network::Address& listenAddr, bool reuseport);
    ~Acceptor();

    // 禁用拷贝构造和赋值
    Acceptor(const Acceptor&) = delete;
    Acceptor& operator=(const Acceptor&) = delete;

    void setNewConnectionCallback(const NewConnectionCallback& cb) { newConnectionCallback_ = cb; }

    void listen();

    bool listening() const { return listening_; }

    // Deprecated, use the correct spelling one above.
    // Leave the wrong spelling here in case one needs to grep it for error messages.
    // bool listenning() const { return listening(); }

private:
    void handleRead(std::chrono::system_clock::time_point);

    EventLoop* loop_;
    zexuan::platform::network::Socket acceptSocket_;
    Channel acceptChannel_;
    NewConnectionCallback newConnectionCallback_;
    bool listening_;
    int idleFd_;
};

} // namespace net
} // namespace zexuan

#endif // ZEXUAN_NET_ACCEPTOR_HPP
