#ifndef ZEXUAN_NET_EVENT_LOOP_THREAD_POOL_HPP
#define ZEXUAN_NET_EVENT_LOOP_THREAD_POOL_HPP

#include <functional>
#include <memory>
#include <vector>
#include <string>
#include "zexuan/net/callbacks.hpp"

namespace zexuan {
namespace net {

class EventLoop;
class EventLoopThread;

class EventLoopThreadPool {
public:
    EventLoopThreadPool(EventLoop* baseLoop, const std::string& nameArg);
    ~EventLoopThreadPool();

    // 禁用拷贝构造和赋值
    EventLoopThreadPool(const EventLoopThreadPool&) = delete;
    EventLoopThreadPool& operator=(const EventLoopThreadPool&) = delete;

    void setThreadNum(int numThreads) { numThreads_ = numThreads; }
    void start(const ThreadInitCallback& cb = ThreadInitCallback());

    // valid after calling start()
    /// round-robin
    EventLoop* getNextLoop();

    /// with the same hash code, it will always return the same EventLoop
    EventLoop* getLoopForHash(size_t hashCode);

    std::vector<EventLoop*> getAllLoops();

    bool started() const { return started_; }

    const std::string& name() const { return name_; }

private:
    EventLoop* baseLoop_;
    std::string name_;
    bool started_;
    int numThreads_;
    int next_;
    std::vector<std::unique_ptr<EventLoopThread>> threads_;
    std::vector<EventLoop*> loops_;
};

} // namespace net
} // namespace zexuan

#endif // ZEXUAN_NET_EVENT_LOOP_THREAD_POOL_HPP
