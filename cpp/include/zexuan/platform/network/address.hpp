#ifndef ZEXUAN_PLATFORM_NETWORK_ADDRESS_HPP
#define ZEXUAN_PLATFORM_NETWORK_ADDRESS_HPP

#include "zexuan/platform/network/sockets_ops.hpp"
#include <string>
#include <cstdint>

// 平台特定的头文件包含
#ifndef __linux__
    #include <winsock2.h>
    #include <ws2tcpip.h>
#else
    #include <netinet/in.h>
    #include <sys/socket.h>
#endif

namespace zexuan {
namespace platform {
namespace network {

// 前向声明
namespace sockets {
const struct sockaddr* sockaddr_cast(const struct sockaddr_in6* addr);
}

class Address {
public:
    // 构造函数 - 与原始 InetAddress 完全一致
    explicit Address(uint16_t port = 0, bool loopbackOnly = false, bool ipv6 = false);
    Address(const std::string& ip, uint16_t port, bool ipv6 = false);
    explicit Address(const struct sockaddr_in& addr);
    explicit Address(const struct sockaddr_in6& addr);

    // 基本方法 - 与原始 InetAddress 完全一致
    sa_family_t family() const { return addr_.sin_family; }
    std::string toIp() const;
    std::string toIpPort() const;
    uint16_t port() const;

    // 系统调用接口 - 与原始 InetAddress 完全一致
    const struct sockaddr* getSockAddr() const { return sockets::sockaddr_cast(&addr6_); }
    void setSockAddrInet6(const struct sockaddr_in6& addr6) { addr6_ = addr6; }

    // 网络字节序 - 与原始 InetAddress 完全一致
    uint32_t ipv4NetEndian() const;
    uint16_t portNetEndian() const { return addr_.sin_port; }

    // 静态方法 - 与原始 InetAddress 完全一致
    static bool resolve(const std::string& hostname, Address* result);

    // IPv6 支持 - 与原始 InetAddress 完全一致
    void setScopeId(uint32_t scope_id);

    // 兼容性方法 - 保持现有接口
    struct sockaddr_in toSockAddr() const;
    struct sockaddr_in6 toSockAddr6() const;
    static Address fromIpPort(const std::string& ip, uint16_t port);
    static Address fromSockAddr(const struct sockaddr& addr);

private:
    // 使用 union 同时支持 IPv4 和 IPv6 - 与原始 InetAddress 完全一致
    union {
        struct sockaddr_in addr_;
        struct sockaddr_in6 addr6_;
    };
};

} // namespace network
} // namespace platform
} // namespace zexuan

#endif // ZEXUAN_PLATFORM_NETWORK_ADDRESS_HPP
