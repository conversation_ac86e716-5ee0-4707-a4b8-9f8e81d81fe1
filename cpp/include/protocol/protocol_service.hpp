#ifndef PROTOCOL_SERVICE_HPP
#define PROTOCOL_SERVICE_HPP

#include <memory>
#include <queue>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <functional>
#include <condition_variable>

#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"

namespace zexuan {
namespace protocol {

// 前向声明
namespace transform {
    class ProtocolTransform;
}

namespace service {

/**
 * @brief 协议服务类
 * 参考原始的 TNXEcMsgOperationObj 设计
 * 负责业务逻辑处理和消息转换
 */
class ProtocolService {
public:
    // 构造函数 - 使用固定ID，每个连接独立的Mediator
    explicit ProtocolService(std::shared_ptr<base::Mediator> mediator);
    virtual ~ProtocolService();

    // 生命周期管理
    bool Initialize();
    bool Start();
    bool Stop();
    bool IsRunning() const { return is_running_; }

    // 设置Transform（用于协议转换）
    void SetProtocolTransform(std::unique_ptr<transform::ProtocolTransform> transform);

    // 事件通过Subject的SendEventNotify方法发送，无需单独的回调设置

private:
    // 基础成员
    std::shared_ptr<base::Mediator> mediator_;

    // Observer和Subject（参考原始设计）
    std::shared_ptr<base::Observer> observer_;
    std::shared_ptr<base::Subject> subject_;

    // 协议转换器
    std::unique_ptr<transform::ProtocolTransform> protocol_transform_;

    // 状态管理
    std::atomic<bool> is_initialized_{false};
    std::atomic<bool> is_running_{false};
    std::atomic<bool> should_stop_{false};

    // 线程管理
    std::thread business_thread_;

    // 自动事件上报功能（参考原始EventOperation）
    std::thread event_report_thread_;
    std::atomic<bool> event_reporting_{false};

    // 消息队列
    std::queue<base::CommonMessage> common_queue_;
    std::mutex common_queue_mutex_;
    std::condition_variable common_queue_cv_;

    // 事件通过Subject发送，无需回调成员变量

    // 消息处理回调
    void OnCommonMessage(const base::CommonMessage& message);
    void OnEventMessage(const base::EventMessage& message);

    // 事件上报线程方法（参考原始EventOperation）
    void EventReportThreadFunc();
    void GenerateTimestampEvent();

    // 线程处理函数
    void BusinessMessageProcessorLoop();

    // 业务逻辑处理
    void ProcessBusinessMessage(const base::CommonMessage& message);
    void SendSingleResponse(const base::CommonMessage& original_message, const base::Message& input_msg);
    void SendMultiFrameResponse(const base::CommonMessage& original_message, const base::Message& input_msg);
    void SendSimpleResponse(const base::CommonMessage& original_message);

    // 队列管理
    bool PushToCommonQueue(const base::CommonMessage& message);
    bool PopFromCommonQueue(base::CommonMessage& message);

    // 工具方法（如果需要的话可以添加）
};

} // namespace service
} // namespace protocol
} // namespace zexuan

#endif // PROTOCOL_SERVICE_HPP