#pragma once

#include "asdu_base.hpp"

namespace zexuan {
namespace protocol {
namespace transform {
namespace gw104 {

/**
 * @brief GW104 ASDU Type2 处理类（多帧测试）
 * 参考原始实现模式
 */
class AsduType2GWS : public AsduBase {
public:
    AsduType2GWS() = default;
    virtual ~AsduType2GWS() = default;

    // 实现基类纯虚函数
    
    /**
     * @brief 解析协议帧为CommonMessage
     * @param frame 协议帧
     * @param common_msg 输出的CommonMessage
     * @return 成功返回0，失败返回错误码
     */
    virtual int ParseToCommonMessage(const base::ProtocolFrame& frame,
                                   base::CommonMessage& common_msg) override;

    /**
     * @brief 将CommonMessage转换为协议帧
     * @param common_msg 输入的CommonMessage
     * @param frame 输出的协议帧
     * @return 成功返回0，失败返回错误码
     */
    virtual int ConvertFromCommonMessage(const base::CommonMessage& common_msg,
                                       base::ProtocolFrame& frame) override;

    /**
     * @brief 获取支持的ASDU类型
     * @return ASDU类型标识
     */
    virtual uint8_t GetSupportedType() const override {
        return 2; // Type 2 = 多帧测试
    }

    /**
     * @brief 获取ASDU类型描述
     * @return 类型描述字符串
     */
    virtual const char* GetTypeDescription() const override {
        return "GW104 ASDU Type2 - Multi-frame Test";
    }

private:
    // 简化版本，不需要复杂的私有方法
};

} // namespace gw104
} // namespace transform
} // namespace protocol
} // namespace zexuan
