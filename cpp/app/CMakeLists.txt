# 创建主应用程序
add_executable(zexuan)

# 设置可执行文件输出目录
set_target_properties(zexuan PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 添加源文件
file(GLOB_RECURSE APP_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/source/*.cpp"
)

target_sources(zexuan
    PRIVATE
        ${APP_SOURCES}
)

# 链接库
target_link_libraries(zexuan
    PRIVATE
        core
        protocol
)

# 设置包含目录
target_include_directories(zexuan
    PRIVATE
        ${CMAKE_SOURCE_DIR}/include
)