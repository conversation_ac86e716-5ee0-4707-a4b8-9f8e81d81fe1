#include <iostream>
#include <memory>
#include <signal.h>
#include <thread>
#include <chrono>
#include <atomic>
#include <spdlog/spdlog.h>

#include "protocol_server.hpp"

using namespace zexuan::protocol::server;

// 全局变量，用于信号处理
std::shared_ptr<ProtocolServer> g_server = nullptr;
std::atomic<bool> g_running{true};

// 信号处理函数
void signalHandler(int signal) {
    spdlog::info("Received signal {}, shutting down server...", signal);
    g_running.store(false);

    // 直接停止服务器，这会退出 EventLoop
    if (g_server) {
        g_server->Stop();
    }
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    spdlog::set_level(spdlog::level::debug);
    spdlog::info("Protocol Test Server starting...");

    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    try {
        // 创建协议服务器，使用配置文件
        std::string config_path = "./config/config.json";
        if (argc > 1) {
            config_path = argv[1];
        }

        spdlog::info("Using config file: {}", config_path);
        g_server = std::make_shared<ProtocolServer>(config_path);

        // 初始化服务器
        if (!g_server->Initialize()) {
            spdlog::error("Failed to initialize protocol server");
            return -1;
        }

        spdlog::info("Protocol server initialized successfully");
        spdlog::info("Server is ready to start");
        spdlog::info("Press Ctrl+C to stop the server");

        // 启动服务器（这会阻塞直到服务器停止）
        if (!g_server->Start()) {
            spdlog::error("Failed to start protocol server");
            return -1;
        }

        spdlog::info("Protocol server has stopped");

        // 服务器已经停止，无需再次调用 Stop()

    } catch (const std::exception& e) {
        spdlog::error("Exception in main: {}", e.what());
        return -1;
    }

    return 0;
}