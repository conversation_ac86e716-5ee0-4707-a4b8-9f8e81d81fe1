# ---- Zexuan Tests ----

# Find or fetch testing framework
find_package(GTest QUIET)
if(NOT GTest_FOUND)
  # If GTest is not found, we can add it via CPM later
  message(STATUS "GTest not found, tests will be skipped")
  return()
endif()

# Enable testing
enable_testing()

# Collect test source files
file(GLOB_RECURSE TEST_SOURCES
  CONFIGURE_DEPENDS
  "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
)

# Only proceed if we have test files
if(NOT TEST_SOURCES)
  message(STATUS "No test files found")
  return()
endif()

# Create test executable
add_executable(zexuan-tests ${TEST_SOURCES})

# Set test properties
set_target_properties(zexuan-tests PROPERTIES
  RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# Link with our libraries and GTest
target_link_libraries(zexuan-tests
  PRIVATE
    zexuan::core
    zexuan::protocol
    GTest::gtest
    GTest::gtest_main
)

# Include directories for tests
target_include_directories(zexuan-tests
  PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Add test to CTest
add_test(NAME zexuan-unit-tests COMMAND zexuan-tests)

# Set test properties
set_tests_properties(zexuan-unit-tests PROPERTIES
  TIMEOUT 300
)
