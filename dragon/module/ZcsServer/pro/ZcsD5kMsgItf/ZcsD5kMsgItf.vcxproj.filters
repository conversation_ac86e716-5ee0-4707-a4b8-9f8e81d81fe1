﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{07228d21-3474-467a-a627-6ad7d2278ad4}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{f7ccd602-586c-4e0b-8f8e-7bb100f0e486}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{6aff582b-65c2-4d50-8eb2-9c04685158f1}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{b502607a-68cf-4ed2-8206-81324f61f3d5}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ZcsD5kMsgItfObserver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxObserver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZcsD5kMsgItf.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZcsD5kMsgItf_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProRun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxRetransmitPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLoadBusSwapLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSendMail.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZclLibmngr_DMail.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxComFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxWaveFileArchiveHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxPackageHdl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxCommonDBFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\load_plmsftp_lib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SendFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\common\ZxDBFacade.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZcsD5kMsgItfObserver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\ZxObserver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZcsD5kMsgItf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProRun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\ZxPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxRetransmitPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\IZxBusSwap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxLoadBusSwapLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxWaveFileArchiveHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxPackageHdl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\Iplm_sftp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\load_plmsftp_lib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SendFile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZcsD5kMsgItf.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
    <None Include="..\..\server\ZcsServer.ini">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>