/********************************************************************
Dragon 2015-04-10 Beijing.
*********************************************************************/

#ifndef XJSpecial103APCIHandler_h__
#define XJSpecial103APCIHandler_h__

#include "Zx103APCIHandler.h"

class CXJSpecial103APCIHandler : public CXJ103APCIHandler
{
public:
    CXJSpecial103APCIHandler(CXJ103MsgAttachFactory* pMsgAttachCreator ,
                                              CLIENT_PRO_INTERFACE* pProInterface ,
                                              CLogFile* pFlowLog ,CLogFile* pMessageLog,
											  CXJMsgCaster* pMsgCaster);
    virtual ~CXJSpecial103APCIHandler();
protected:
	virtual bool InitStateObjects();
    virtual int							  SendAndRecvMessage(const MESSAGE103& pSendMsg, 
													MESSAGE103& pRecvMsg, int pExpectedFunType);
	virtual int SelectingData(const ASDUMESSAGE& ASDUData);
};
#endif // XJSpecial103APCIHandler_h__