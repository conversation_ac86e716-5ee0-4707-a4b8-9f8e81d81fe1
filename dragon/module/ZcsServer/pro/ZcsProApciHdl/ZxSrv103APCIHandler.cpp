/********************************************************************
Dragon 2015-04-10 Beijing.
*********************************************************************/
#include "ZxSrv103APCIHandler.h"

CXJSrv103APCIHandler::CXJSrv103APCIHandler(CXJ103MsgAttachFactory* pMsgAttachCreator,
	SERVER_PRO_INTERFACE* pProInterface, 
	CLogFile* pFlowLog ,CLogFile* pMessageLog, 
	CXJMsgCaster* pMsgCaster )
:CXJSrvAPCIHandler(pProInterface,pFlowLog,pMessageLog,pMsgCaster)
{
	
}

CXJSrv103APCIHandler::~CXJSrv103APCIHandler()
{
	
}

bool CXJSrv103APCIHandler::Start( void )
{
	return false;
}

void CXJSrv103APCIHandler::End( void )
{
	
}

int CXJSrv103APCIHandler::SetTimeOut( TIMEOUT_103& pTimeOut )
{
	return 0;
}

void CXJSrv103APCIHandler::WriteLog( const char * pLog,int nLevel )
{
	if ((NULL != pLog) && (NULL != m_pLogFile))
	{
		m_pLogFile->FormatAdd(nLevel,"[SESSION:%d CXJSrv103APCIHandler] %s",m_iSessionNO,pLog);
	}	
}

void CXJSrv103APCIHandler::MakeLinkDescription( const MESSAGE103& pMsg, string& pDescString )
{
	
}
