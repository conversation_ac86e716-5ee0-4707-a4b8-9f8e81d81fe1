﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{87d27589-e6c4-45c1-a6fe-93b246e1a03a}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{f065a994-5907-48af-bb6f-1d557a6a79ce}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{0435a24b-064d-4970-ac2e-d9bc9d6dae27}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{54d8f2ec-cb52-4f70-941d-42b4a408d60c}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Zx103APCIHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Zx103InitLinkState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Zx103InitLinkStateIgnoreCOT.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Zx103NoLinkState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Zx103PollingLevel1State.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Zx103PollingLevel2State.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Zx103PreprocessState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Zx103SendDataState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Zx104APCIHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxAPCIHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxComFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxMsg104SerialNum.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxMsgCaster.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProAPCIHadler_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProAPCIHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSpecial103APCIHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv103APCIHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv104APCIHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv104AssureStopDTState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv104AssureTestDTState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv104NoLinkState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv104RecvMsgState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv104SendDataState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv104SendSMsgState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrv104ValidTestDTState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSrvAPCIHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_MsgMonitor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Zx103APCIHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx103InitLinkState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx103InitLinkStateIgnoreCOT.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx103LinkState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\Zx103MsgAttach.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\Zx103MsgAttachFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx103NoLinkState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx103PollingLevel1State.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx103PollingLevel2State.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx103PreprocessState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\Zx103ProtocolDef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx103SendDataState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Zx104APCIHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\Zx104MsgAttach.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\Zx104MsgAttachFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxAPCIHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxAPCIInterFace.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\ZxDBFacade.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxMsg104SerialNum.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\ZxMsgCaster.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProAPCIHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\ZxProtocolAPI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSpecial103APCIHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv103LinkState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv104AssureStopDTState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv104AssureTestDTState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv104LinkState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv104NoLinkState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv104RecvMsgState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv104SendDataState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv104SendSMsgState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSrv104ValidTestDTState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZxProAPCIHandler.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>