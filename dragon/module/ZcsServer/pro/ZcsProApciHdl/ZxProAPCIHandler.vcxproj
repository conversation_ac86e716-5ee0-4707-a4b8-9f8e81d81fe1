﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\Release\</OutDir>
    <IntDir>.\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>..\..\..\..\common\head;..\..\..\..\libapimngr;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\release;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\Debug\</OutDir>
    <IntDir>.\Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>..\..\..\..\common\head;..\..\..\..\libapimngr;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\debug;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PreprocessorDefinitions>NDEBUG;WIN32;_WINDOWS;_USRDLL;XJPROAPCIHANDLER_EXPORTS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Release\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\Release\ZxProAPCIHandler.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\Release\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Release\ZxProAPCIHandler.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release\ZxProAPCIHandler.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <SubSystem>Console</SubSystem>
      <OutputFile>../../../../bin/$(Configuration)/ZcsServer/ZcsProApciHdl.dll</OutputFile>
      <ImportLibrary>.\Release\ZxProAPCIHandler.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;ws2_32.lib;zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;XJPROAPCIHANDLER_EXPORTS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Debug\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\Debug\ZxProAPCIHandler.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Debug\ZxProAPCIHandler.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug\ZxProAPCIHandler.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>../../../../bin/$(Configuration)/ZcsServer/ZcsProApciHdl.dll</OutputFile>
      <ImportLibrary>.\Debug\ZxProAPCIHandler.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;ws2_32.lib;zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_MsgMonitor.cpp" />
    <ClCompile Include="Zx103APCIHandler.cpp" />
    <ClCompile Include="Zx103InitLinkState.cpp" />
    <ClCompile Include="Zx103InitLinkStateIgnoreCOT.cpp" />
    <ClCompile Include="Zx103NoLinkState.cpp" />
    <ClCompile Include="Zx103PollingLevel1State.cpp" />
    <ClCompile Include="Zx103PollingLevel2State.cpp" />
    <ClCompile Include="Zx103PreprocessState.cpp" />
    <ClCompile Include="Zx103SendDataState.cpp" />
    <ClCompile Include="Zx104APCIHandler.cpp" />
    <ClCompile Include="ZxAPCIHandler.cpp" />
    <ClCompile Include="..\common\ZxComFunction.cpp" />
    <ClCompile Include="ZxMsg104SerialNum.cpp" />
    <ClCompile Include="..\..\common\ZxMsgCaster.cpp" />
    <ClCompile Include="ZxProAPCIHadler_update_history.cpp" />
    <ClCompile Include="ZxProAPCIHandler.cpp" />
    <ClCompile Include="ZxSpecial103APCIHandler.cpp" />
    <ClCompile Include="ZxSrv103APCIHandler.cpp" />
    <ClCompile Include="ZxSrv104APCIHandler.cpp" />
    <ClCompile Include="ZxSrv104AssureStopDTState.cpp" />
    <ClCompile Include="ZxSrv104AssureTestDTState.cpp" />
    <ClCompile Include="ZxSrv104NoLinkState.cpp" />
    <ClCompile Include="ZxSrv104RecvMsgState.cpp" />
    <ClCompile Include="ZxSrv104SendDataState.cpp" />
    <ClCompile Include="ZxSrv104SendSMsgState.cpp" />
    <ClCompile Include="ZxSrv104ValidTestDTState.cpp" />
    <ClCompile Include="ZxSrvAPCIHandler.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZxProAPCIHandler.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="resource.h" />
    <ClInclude Include="Zx103APCIHandler.h" />
    <ClInclude Include="Zx103InitLinkState.h" />
    <ClInclude Include="Zx103InitLinkStateIgnoreCOT.h" />
    <ClInclude Include="Zx103LinkState.h" />
    <ClInclude Include="..\common\Zx103MsgAttach.h" />
    <ClInclude Include="..\common\Zx103MsgAttachFactory.h" />
    <ClInclude Include="Zx103NoLinkState.h" />
    <ClInclude Include="Zx103PollingLevel1State.h" />
    <ClInclude Include="Zx103PollingLevel2State.h" />
    <ClInclude Include="Zx103PreprocessState.h" />
    <ClInclude Include="..\common\Zx103ProtocolDef.h" />
    <ClInclude Include="Zx103SendDataState.h" />
    <ClInclude Include="Zx104APCIHandler.h" />
    <ClInclude Include="..\common\Zx104MsgAttach.h" />
    <ClInclude Include="..\common\Zx104MsgAttachFactory.h" />
    <ClInclude Include="ZxAPCIHandler.h" />
    <ClInclude Include="..\common\ZxAPCIInterFace.h" />
    <ClInclude Include="..\..\common\ZxDBFacade.h" />
    <ClInclude Include="ZxMsg104SerialNum.h" />
    <ClInclude Include="..\..\common\ZxMsgCaster.h" />
    <ClInclude Include="ZxProAPCIHandler.h" />
    <ClInclude Include="..\..\common\ZxProtocolAPI.h" />
    <ClInclude Include="ZxSpecial103APCIHandler.h" />
    <ClInclude Include="ZxSrv103LinkState.h" />
    <ClInclude Include="ZxSrv104AssureStopDTState.h" />
    <ClInclude Include="ZxSrv104AssureTestDTState.h" />
    <ClInclude Include="ZxSrv104LinkState.h" />
    <ClInclude Include="ZxSrv104NoLinkState.h" />
    <ClInclude Include="ZxSrv104RecvMsgState.h" />
    <ClInclude Include="ZxSrv104SendDataState.h" />
    <ClInclude Include="ZxSrv104SendSMsgState.h" />
    <ClInclude Include="ZxSrv104ValidTestDTState.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>