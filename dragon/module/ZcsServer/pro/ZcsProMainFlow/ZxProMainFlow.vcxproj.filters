﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{813e763c-3a04-4ca9-a572-ffa4503e4c99}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{81fec25f-e824-4412-8413-58085c323a55}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{db746c23-9795-4cec-b185-e5612cb2e266}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{0fedbc58-8ab7-430c-9d77-1b89df854574}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Zx103CacheIndex.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxComFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxCommonDBFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxPro103ClientWay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProCFDownloadOperation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProCtrlCommandOperation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProInitOperation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProMainFlow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProMainFlow_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProResultOperation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSubStationPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxCommuServer_protocol_common_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Zx103CacheIndex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxPro103ClientWay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProCFDownloadOperation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProCtrlCommandOperation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProInitOperation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProMainFlow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProOperation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProResultOperation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxModelSend.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZxProMainFlow.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>