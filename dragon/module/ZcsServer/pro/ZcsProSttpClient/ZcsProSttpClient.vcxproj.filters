﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{340b77f7-d0f5-4c75-bac7-3e2c961026f4}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{2e370e48-070f-4b82-a606-bc3c9727e5b7}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{30990ce5-d057-400a-8913-3ddcfcd2e44f}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\common\SttpMsgProductor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="XJProRun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZcsProSttpClient_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZcsProSttpClientWay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSubStationPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DbAgent.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZclLibmngr_Gbk2Utf8.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinystr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxml.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxmlerror.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxmlparser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSttpClientPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\common\CommuDef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\CsLocker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ProDataStruct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\SttpMsgProductor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="XJProRun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZcsProSttpClientWay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSubStationPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\ZxPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\ZxProtocolAPI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_DbAgent.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZclLibmngr_Gbk2Utf8.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\thirdparty\tinyxml\tinystr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\thirdparty\tinyxml\tinyxml.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSttpClientPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZcsProSttpClient.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>