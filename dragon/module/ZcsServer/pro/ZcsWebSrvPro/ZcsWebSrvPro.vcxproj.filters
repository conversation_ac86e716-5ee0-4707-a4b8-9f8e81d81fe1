﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="ZxProWebSrvWay.cpp">
      <Filter>Cpp</Filter>
    </ClCompile>
    <ClCompile Include="ZxProRun.cpp">
      <Filter>Cpp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibApi_CvtStnXmlToDb.cpp">
      <Filter>Cpp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp">
      <Filter>Cpp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp">
      <Filter>Cpp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibmngr_zipc.cpp">
      <Filter>Cpp</Filter>
    </ClCompile>
    <ClCompile Include="ZcsWebSrvPro_update_history.cpp">
      <Filter>Cpp</Filter>
    </ClCompile>
    <ClCompile Include="SendFile.cpp">
      <Filter>Cpp</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="resource.h">
      <Filter>Head</Filter>
    </ClInclude>
    <ClInclude Include="ZxProRun.h">
      <Filter>Head</Filter>
    </ClInclude>
    <ClInclude Include="ZxProWebSrvWay.h">
      <Filter>Head</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibApi_CvtStnXmlToDb.h">
      <Filter>Head</Filter>
    </ClInclude>
    <ClInclude Include="SendFile.h">
      <Filter>Head</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZxProSttpServer.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
    <None Include="..\..\server\ZcsServer.ini">
      <Filter>Ini</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Cpp">
      <UniqueIdentifier>{c88ca6ed-b7f7-4bf2-b9d0-4b5f875f8d5e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Head">
      <UniqueIdentifier>{dff4a8b3-e6e7-4407-aeb4-5dc6b1a9559a}</UniqueIdentifier>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{2fd6549c-94b6-4f36-80d3-8c73baaee3dc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Ini">
      <UniqueIdentifier>{c307ae5d-df42-43dc-9f16-8e815916e83d}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>