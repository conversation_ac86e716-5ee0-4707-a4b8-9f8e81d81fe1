﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\Debug\</OutDir>
    <IntDir>.\Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>..\..\..\..\common\head;..\..\..\..\libapimngr;..\common\;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\Release\</OutDir>
    <IntDir>.\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>..\..\..\..\common\head;..\..\..\..\libapimngr;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <RuntimeTypeInfo>
      </RuntimeTypeInfo>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;XJPROSTTPSERVER_EXPORTS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Debug\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\Debug\ZcsWebSrvPro.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <StringPooling>
      </StringPooling>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Debug\ZcsWebSrvPro.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug\ZcsWebSrvPro.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>..\..\..\..\bin\$(Configuration)\ZcsServer\ZcsWebSrvPro.dll</OutputFile>
      <ImportLibrary>.\Debug\ZcsWebSrvPro.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;ws2_32.lib;zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;XJPROSTTPSERVER_EXPORTS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Release\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\Release\ZcsWebSrvPro.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\Release\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Release\ZcsWebSrvPro.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release\ZcsWebSrvPro.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <SubSystem>Console</SubSystem>
      <OutputFile>..\..\..\..\bin\$(Configuration)\ZcsServer\ZcsWebSrvPro.dll</OutputFile>
      <ImportLibrary>.\Release\ZcsWebSrvPro.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;ws2_32.lib;zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibApi_CvtStnXmlToDb.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibmngr_zipc.cpp" />
    <ClCompile Include="SendFile.cpp" />
    <ClCompile Include="ZcsWebSrvPro_update_history.cpp" />
    <ClCompile Include="ZxProRun.cpp" />
    <ClCompile Include="ZxProWebSrvWay.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZxProSttpServer.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibApi_CvtStnXmlToDb.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="SendFile.h" />
    <ClInclude Include="ZxProRun.h" />
    <ClInclude Include="ZxProWebSrvWay.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\server\ZcsServer.ini" />
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>