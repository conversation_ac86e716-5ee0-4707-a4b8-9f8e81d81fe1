﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\Debug\</OutDir>
    <IntDir>.\Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>.E:\SVN\platform\trunk\external\cosmos_mstn\inc;..\..\..\..\platform\platform_include\plm_common;..\..\..\..\common\head;..\..\..\..\libapimngr;..\..\..\..\thirdparty\d5k\include;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\debug;E:\SVN20171106\platform\trunk\external\cosmos\lib\win32;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\Release\</OutDir>
    <IntDir>.\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>..\..\..\..\common\head;..\..\..\..\libapimngr;..\..\..\..\thirdparty\d5k\include;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\release\;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;XJPROMAINFLOW_EXPORTS;__PLATFORM_MS_WIN__;OS_WINDOWS;MMS_LITE;ETHERNET;DEBUG_SISCO;MOSI;LEAN_T;MVL_UCA%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Debug\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\Debug\ZcsPro61850Cli.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <AdditionalIncludeDirectories>./../common;../../../../platform/platform_include/external/cosmos_mstn/inc;../../../../platform/platform_include/external/cosmos_mstn/mmslite/inc;../../../../platform/platform_include/plm_common/</AdditionalIncludeDirectories>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Debug\ZcsPro61850Cli.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug\ZcsPro61850Cli.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>../../../../bin/$(Configuration)/ZcsServer/ZcsPro61850Cli.dll</OutputFile>
      <ImportLibrary>.\Debug\ZcsPro61850Cli.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;zxcommon.lib;acsi_ld.lib;mmslite_ld.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../../../../platform/trunk/external/cosmos/lib/win32;</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;XJPROMAINFLOW_EXPORTS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Release\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\Release\ZcsPro61850Cli.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\Release\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Release\ZcsPro61850Cli.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release\ZcsPro61850Cli.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <SubSystem>Console</SubSystem>
      <OutputFile>../../../../bin/$(Configuration)/ZcsServer/ZcsPro61850Cli.dll</OutputFile>
      <ImportLibrary>.\Release\ZcsPro61850Cli.lib</ImportLibrary>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp" />
    <ClCompile Include="..\common\Zx61850DBFunction.cpp" />
    <ClCompile Include="..\common\Zx61850MD5.cpp" />
    <ClCompile Include="ioapi.c" />
    <ClCompile Include="ThreadManger.cpp" />
    <ClCompile Include="..\common\ZxComFunction.cpp" />
    <ClCompile Include="..\common\ZxCommonDBFunction.cpp" />
    <ClCompile Include="unzip.c" />
    <ClCompile Include="Zx61850FuncHandle.cpp" />
    <ClCompile Include="ZxPro61850Flow.cpp" />
    <ClCompile Include="ZcsPro61850Cli.cpp" />
    <ClCompile Include="ZcsPro61850Cli_update_history.cpp" />
    <ClCompile Include="ZxProResultOperation.cpp" />
    <ClCompile Include="..\..\common\ZxPublisher.cpp" />
    <ClCompile Include="..\common\ZxSubStationPublisher.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZcsPro61850Cli.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\common\iec61850define.h" />
    <ClInclude Include="..\common\Zx61850DBFunction.h" />
    <ClInclude Include="..\common\Zx61850MD5.h" />
    <ClInclude Include="..\common\ZxCommonDBFunction.h" />
    <ClInclude Include="ioapi.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="ThreadManger.h" />
    <ClInclude Include="unzip.h" />
    <ClInclude Include="zconf.h" />
    <ClInclude Include="zlib.h" />
    <ClInclude Include="Zx61850FuncHandle.h" />
    <ClInclude Include="ZxPro61850Flow.h" />
    <ClInclude Include="ZcsPro61850Cli.h" />
    <ClInclude Include="ZxProOperation.h" />
    <ClInclude Include="ZxProResultOperation.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
    <None Include="ZcsPro61850CliNW.ini" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>