﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\Release\</OutDir>
    <IntDir>.\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>..\..\..\..\common\head;..\..\..\..\libapimngr;..\..\..\..\thirdparty\d5k\include;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\Debug\</OutDir>
    <IntDir>.\Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>..\..\..\..\common\head;..\..\..\..\libapimngr;..\..\..\..\thirdparty\d5k\include;..\..\..\..\thirdparty\unicorn\paltform_include\plm_common\;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;OS_WINDOWS;%(PreprocessorDefinitions);_CRT_SECURE_NO_WARNINGS;</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Release\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\Release\ZcsSbItfCli.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\Release\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\Release\ZcsSbItfCli.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release\ZcsSbItfCli.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <SubSystem>Console</SubSystem>
      <OutputFile>..\..\..\..\bin\$(Configuration)\ZcsServer\ZcsSbItfCli.dll</OutputFile>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;OS_WINDOWS;%(PreprocessorDefinitions);_CRT_SECURE_NO_WARNINGS;</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Debug\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\Debug\ZcsSbItfCli.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\Debug\ZcsSbItfCli.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug\ZcsSbItfCli.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>..\..\..\..\bin\$(Configuration)\ZcsServer\ZcsSbItfCli.dll</OutputFile>
      <AdditionalDependencies>zxcommon.lib;</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\libapimngr\load_plmsftp_lib.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZclLibmngr_DMail.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLoadBusSwapLib.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp" />
    <ClCompile Include="..\common\ZxComFunction.cpp" />
    <ClCompile Include="..\common\ZxCommonDBFunction.cpp" />
    <ClCompile Include="..\common\ZxRetransmitPublisher.cpp" />
    <ClCompile Include="SendFile.cpp" />
    <ClCompile Include="ZcsSbItfCliObserver.cpp" />
    <ClCompile Include="..\..\common\ZxObserver.cpp" />
    <ClCompile Include="ZcsSbItfCli.cpp" />
    <ClCompile Include="ZcsSbItfCli_update_history.cpp" />
    <ClCompile Include="ZxPackageHdl.cpp" />
    <ClCompile Include="ZxProRun.cpp" />
    <ClCompile Include="..\..\common\ZxPublisher.cpp" />
    <ClCompile Include="ZxSttpBusSwap.cpp" />
    <ClCompile Include="ZxWaveFileArchiveHandler.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\libapimngr\load_plmsftp_lib.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.h" />
    <ClInclude Include="..\..\common\ZxDBFacade.h" />
    <ClInclude Include="..\common\ZxRetransmitPublisher.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="SendFile.h" />
    <ClInclude Include="ZcsSbItfCliObserver.h" />
    <ClInclude Include="..\..\common\ZxObserver.h" />
    <ClInclude Include="ZcsSbItfCli.h" />
    <ClInclude Include="ZxPackageHdl.h" />
    <ClInclude Include="ZxProRun.h" />
    <ClInclude Include="..\..\common\ZxPublisher.h" />
    <ClInclude Include="ZxSttpBusSwap.h" />
    <ClInclude Include="ZxWaveFileArchiveHandler.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZcsSbItfCli.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\server\ZcsServer.ini" />
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>