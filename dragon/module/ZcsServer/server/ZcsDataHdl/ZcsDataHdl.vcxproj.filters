﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{ae652280-4716-4ded-a7a9-dcd01fa5cce3}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{d1e1fdec-9dbf-463e-b216-3aa44e40a722}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{28eb5fa0-3e9a-4c72-a6de-b9d53b27d605}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{fe75f38f-7c85-479c-a4a4-07e153504419}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="DataHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZcsDataHdlPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZcsDataHdl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZcsDataHdl_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_WaveCfgBindDevice.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxCommuServer_common_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="DataHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="OscilloAnalystWrapper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\XJPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZcsDataHdlPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZcsDataHdl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_WaveCfgBindDevice.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZcsDataHdl.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>