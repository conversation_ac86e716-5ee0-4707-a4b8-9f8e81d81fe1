﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B8E32959-E039-4E14-B9DE-B563B3EFFBCD}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>ZasFltSceKeeper</RootNamespace>
    <ProjectName>ZcsSrvMngr</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>..\..\..\..\bin\$(Configuration)\ZcsServer\$(ProjectName)</OutDir>
    <IncludePath>..\..\..\..\common\head;..\..\..\..\thirdparty\tinyxml;..\..\..\..\libapimngr;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\lib\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..\..\..\common\head;..\..\..\..\libapimngr;..\..\..\..\thirdparty\tinyxml;..\..\common;..\..\pro\common;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\..\lib\debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\common\head\ZxGlobal_Def.h" />
    <ClInclude Include="..\..\..\..\common\head\ZxLockableObject.h" />
    <ClInclude Include="..\..\..\..\common\head\ZxMessageLog.h" />
    <ClInclude Include="..\..\..\..\common\head\ZxNetAcceptor.h" />
    <ClInclude Include="..\..\..\..\common\head\ZxNetConnector.h" />
    <ClInclude Include="..\..\..\..\common\head\ZxNetStream.h" />
    <ClInclude Include="..\..\..\..\common\head\ZxSttpDefine.h" />
    <ClInclude Include="..\..\..\..\common\head\ZxSttpMsgParser.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxLoadBusSwapLib.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.h" />
    <ClInclude Include="DBOper.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="ServerManager.h" />
    <ClInclude Include="SttpBusItf.h" />
    <ClInclude Include="ZcsSrvMngr.h" />
    <ClInclude Include="ZxSrvMngrPublisher.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLoadBusSwapLib.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp" />
    <ClCompile Include="DBOper.cpp" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="ServerManager.cpp" />
    <ClCompile Include="SttpBusItf.cpp" />
    <ClCompile Include="ZcsSrvMngr.cpp" />
    <ClCompile Include="ZxSrvMngrPublisher.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
    <None Include="ZcsSrvMngr_update_history.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZcsSrvMngr.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>