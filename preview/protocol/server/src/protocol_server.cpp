#include "../include/protocol_server.hpp"
#include "protocol_gateway.hpp"
#include <spdlog/spdlog.h>
#include <fstream>

namespace zexuan {
namespace protocol{
namespace server {

ProtocolServer::ProtocolServer(const std::string& config_file_path)
    : config_file_path_(config_file_path) {

    // 读取配置文件
    std::ifstream config_file(config_file_path_);
    if (!config_file.is_open()) {
        throw std::runtime_error("Cannot open config file: " + config_file_path_);
    }

    nlohmann::json config;
    config_file >> config;

    // 读取服务器配置
    auto server_config = config["protocol"]["server"];
    listen_address_ = server_config.value("listen_address", "0.0.0.0");
    listen_port_ = server_config.value("listen_port", 8080);
    max_connections_ = server_config.value("max_connections", 100);
    thread_pool_size_ = server_config.value("thread_pool_size", 4);

    spdlog::info("ProtocolServer created with config: {}:{}, max_conn={}, threads={}",
                 listen_address_, listen_port_, max_connections_, thread_pool_size_);
}

ProtocolServer::~ProtocolServer() {
    Shutdown();
    spdlog::info("ProtocolServer destroyed");
}

bool ProtocolServer::Initialize() {
    if (is_initialized_.load()) {
        spdlog::warn("ProtocolServer already initialized");
        return true;
    }

    try {
        // 创建EventLoop
        event_loop_ = std::make_unique<net::EventLoop>();

        // 创建TcpServer
        zexuan::platform::network::Address listen_addr(listen_address_, listen_port_);
        tcp_server_ = std::make_unique<net::TcpServer>(event_loop_.get(), listen_addr, "ProtocolServer");

        // 设置回调
        tcp_server_->setConnectionCallback([this](const net::TcpConnectionPtr& conn) {
            OnConnection(conn);
        });

        tcp_server_->setMessageCallback([this](const net::TcpConnectionPtr& conn, net::Buffer* buffer, net::Timestamp receiveTime) {
            OnMessage(conn, buffer, receiveTime);
        });

        tcp_server_->setWriteCompleteCallback([this](const net::TcpConnectionPtr& conn) {
            OnWriteComplete(conn);
        });

        // 设置线程池大小
        tcp_server_->setThreadNum(thread_pool_size_);

        is_initialized_.store(true);
        spdlog::info("ProtocolServer initialized successfully on {}:{}", listen_address_, listen_port_);
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to initialize ProtocolServer: {}", e.what());
        return false;
    }
}

bool ProtocolServer::Start() {
    if (!is_initialized_.load()) {
        spdlog::error("ProtocolServer not initialized");
        return false;
    }

    if (is_running_.load()) {
        spdlog::warn("ProtocolServer already running");
        return true;
    }

    try {
        // 启动TcpServer
        tcp_server_->start();

        is_running_.store(true);
        spdlog::info("ProtocolServer started successfully");

        // 运行事件循环（这会阻塞）
        event_loop_->loop();

        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to start ProtocolServer: {}", e.what());
        return false;
    }
}



bool ProtocolServer::Stop() {
    if (!is_running_.load()) {
        // 静默返回，避免重复警告
        return true;
    }

    try {
        // 停止事件循环
        event_loop_->quit();

        is_running_.store(false);
        spdlog::info("ProtocolServer stopped successfully");
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to stop ProtocolServer: {}", e.what());
        return false;
    }
}

void ProtocolServer::Shutdown() {
    // 只在还在运行时才调用 Stop，避免重复调用
    if (is_running_.load()) {
        Stop();
    }

    // 清理所有连接和Gateway
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        for (const auto& conn : connections_) {
            if (conn && conn->connected()) {
                conn->forceClose();
            }
            DestroyGatewayForConnection(conn);
        }
        connections_.clear();
    }



    // 清理网络组件
    tcp_server_.reset();
    event_loop_.reset();

    is_initialized_.store(false);
    spdlog::info("ProtocolServer shutdown completed");
}

bool ProtocolServer::SendData(const net::TcpConnectionPtr& conn, const std::vector<uint8_t>& data) {
    if (conn && conn->connected()) {
        conn->send(data.data(), data.size());
        stats_.bytes_sent += data.size();
        spdlog::debug("Sent {} bytes to connection {}", data.size(), conn->name());
        return true;
    }

    spdlog::error("Failed to send data: connection not found or disconnected");
    return false;
}

// 网络回调处理
void ProtocolServer::OnConnection(const net::TcpConnectionPtr& conn) {
    if (conn->connected()) {
        // 新连接建立
        AddConnection(conn);

        // 为连接创建Gateway
        if (CreateGatewayForConnection(conn)) {
            stats_.total_connections++;
            stats_.active_connections++;
            spdlog::info("New connection established: {}", conn->name());
        } else {
            spdlog::error("Failed to create Gateway for connection: {}", conn->name());
            conn->forceClose();
        }
    } else {
        // 连接断开
        RemoveConnection(conn);
        spdlog::info("Connection closed: {}", conn->name());
    }
}

void ProtocolServer::OnMessage(const net::TcpConnectionPtr& conn, net::Buffer* buffer, net::Timestamp receiveTime) {
    // 统计接收字节数
    stats_.bytes_received += buffer->readableBytes();

    // 在一次回调中处理所有完整帧（直接使用TcpConnection）
    while (ExtractOneFrame(buffer, conn)) {
        // 继续提取下一帧
    }
}

void ProtocolServer::OnWriteComplete(const net::TcpConnectionPtr& conn) {
    spdlog::debug("Write completed for connection: {}", conn->name());
}

bool ProtocolServer::ExtractOneFrame(net::Buffer* buffer, const net::TcpConnectionPtr& conn) {
    // 检查最小帧长度（0x68 + LEN_L + LEN_H）
    if (buffer->readableBytes() < 3) {
        return false;
    }

    // 查找帧起始符 0x68
    const char* start = static_cast<const char*>(
        memchr(buffer->peek(), 0x68, buffer->readableBytes())
    );

    if (!start) {
        // 没有找到起始符，丢弃所有数据
        size_t discarded = buffer->readableBytes();
        buffer->retrieveAll();
        spdlog::warn("Connection {}: No frame start found, discarding {} bytes", conn->name(), discarded);
        return false;
    }

    // 跳过起始符之前的垃圾数据
    size_t skip = start - buffer->peek();
    if (skip > 0) {
        buffer->retrieve(skip);
        spdlog::warn("Connection {}: Skipped {} bytes of garbage data", conn->name(), skip);
    }

    // 再次检查是否有足够的数据读取帧头
    if (buffer->readableBytes() < 3) {
        return false;
    }

    // 解析帧长度：LEN_L + LEN_H
    uint8_t len_l = static_cast<uint8_t>(buffer->peek()[1]);
    uint8_t len_h = static_cast<uint8_t>(buffer->peek()[2]);
    uint16_t asdu_len = len_l | (static_cast<uint16_t>(len_h) << 8);
    size_t total_frame_len = 3 + asdu_len;  // START + LEN_L + LEN_H + ASDU

    // 帧长度合法性检查（IEC 60870-5-103 最大帧长度约1024字节）
    if (asdu_len > 1024) {
        buffer->retrieve(1);  // 跳过这个错误的起始符
        spdlog::warn("Connection {}: Invalid frame length {}, skipping", conn->name(), asdu_len);
        return true;  // 继续尝试下一个字节
    }

    // 检查是否有完整帧
    if (buffer->readableBytes() < total_frame_len) {
        // 不完整帧：保留在 buffer 中，等待下次 onMessage
        spdlog::debug("Connection {}: Incomplete frame, need {} bytes, have {} bytes",
                     conn->name(), total_frame_len, buffer->readableBytes());
        return false;
    }

    // 提取完整帧
    std::vector<uint8_t> frame_data(total_frame_len);
    memcpy(frame_data.data(), buffer->peek(), total_frame_len);
    buffer->retrieve(total_frame_len);

    // 发送给 Gateway
    SendFrameToGateway(conn, frame_data);

    spdlog::debug("Connection {}: Extracted complete frame of {} bytes", conn->name(), total_frame_len);
    return true;  // 继续处理下一帧
}

void ProtocolServer::SendFrameToGateway(const net::TcpConnectionPtr& conn, const std::vector<uint8_t>& frame_data) {
    std::lock_guard<std::mutex> lock(gateways_mutex_);
    auto gateway_it = gateways_.find(conn);
    if (gateway_it != gateways_.end()) {
        // 发送完整帧给Gateway处理（每个Gateway对应一个连接）
        gateway_it->second->OnNetworkProtocolData(frame_data);
        spdlog::debug("Sent complete frame to Gateway for connection {}: {} bytes", conn->name(), frame_data.size());
    } else {
        spdlog::error("No Gateway found for connection {}", conn->name());
    }
}

// 连接管理方法（简化：直接管理TcpConnection）
void ProtocolServer::AddConnection(const net::TcpConnectionPtr& conn) {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    connections_.insert(conn);
}

void ProtocolServer::RemoveConnection(const net::TcpConnectionPtr& conn) {
    std::lock_guard<std::mutex> lock(connections_mutex_);

    // 销毁对应的Gateway
    DestroyGatewayForConnection(conn);

    // 移除连接
    connections_.erase(conn);

    stats_.active_connections--;
    spdlog::info("Connection removed: {}", conn->name());
}

// Gateway管理方法（直接使用TcpConnection）
bool ProtocolServer::CreateGatewayForConnection(const net::TcpConnectionPtr& conn) {
    try {
        // 创建 Mediator（每个Gateway需要独立的Mediator）
        auto mediator = std::make_shared<base::Mediator>();

        // 直接创建 Gateway，传递配置文件路径和 Mediator（使用重构后的构造函数）
        auto gateway = std::make_shared<protocol::gateway::ProtocolGateway>(
            config_file_path_, mediator);

        // 设置Gateway的发送回调，让它能够通过TCPServer发送数据（每个Gateway对应一个连接）
        gateway->SetSendCallback([this, conn](const std::vector<uint8_t>& data) -> bool {
            return SendData(conn, data);
        });

        // 启动Gateway
        if (!gateway->Start()) {
            spdlog::error("Failed to start Gateway for connection {}", conn->name());
            return false;
        }

        // 存储Gateway
        {
            std::lock_guard<std::mutex> lock(gateways_mutex_);
            gateways_[conn] = gateway;
        }

        spdlog::info("Created Gateway for connection {}", conn->name());
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Exception creating Gateway for connection {}: {}", conn->name(), e.what());
        return false;
    }
}

void ProtocolServer::DestroyGatewayForConnection(const net::TcpConnectionPtr& conn) {
    std::lock_guard<std::mutex> lock(gateways_mutex_);
    auto it = gateways_.find(conn);
    if (it != gateways_.end()) {
        // 停止Gateway
        it->second->Stop();

        // 移除Gateway
        gateways_.erase(it);

        spdlog::info("Destroyed Gateway for connection {}", conn->name());
    }
}




// 状态查询方法
size_t ProtocolServer::GetConnectionCount() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);
    return connections_.size();
}

std::vector<std::string> ProtocolServer::GetAllConnectionNames() const {
    std::lock_guard<std::mutex> lock(connections_mutex_);

    std::vector<std::string> names;
    names.reserve(connections_.size());

    for (const auto& conn : connections_) {
        names.push_back(conn->name());
    }

    return names;
}

void ProtocolServer::ResetStatistics() {
    stats_.total_connections.store(0);
    stats_.active_connections.store(0);
    stats_.bytes_sent.store(0);
    stats_.bytes_received.store(0);
}

} // namespace server
} // namespace protocol
} // namespace zexuan