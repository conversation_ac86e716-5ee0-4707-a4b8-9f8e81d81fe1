#include "protocol_service.hpp"
#include "protocol_transform.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace service {

ProtocolService::ProtocolService(std::shared_ptr<base::Mediator> mediator)
    : mediator_(mediator) {

    // 创建Observer - 使用固定ID接收来自Gateway的命令
    observer_ = std::make_shared<base::Observer>(base::SERVICE_OBSERVER_ID, mediator_);

    // 设置Observer的事件回调 - 处理来自Gateway的EventMessage
    observer_->SetEventCallback([this](const base::EventMessage& message) -> base::Result<void> {
        OnEventMessage(message);
        return base::Result<void>{};
    });

    // 创建Subject - 使用固定ID向Gateway发送响应和事件
    subject_ = std::make_shared<base::Subject>(base::SERVICE_SUBJECT_ID, mediator_);

    // 设置Subject的命令回调 - 处理来自Gateway的命令
    subject_->SetCmdCallback([this](const base::CommonMessage& message) -> base::Result<void> {
        OnCommonMessage(message);
        return base::Result<void>{};
    });

    spdlog::info("ProtocolService created with Observer ID: {}, Subject ID: {}",
                 base::SERVICE_OBSERVER_ID, base::SERVICE_SUBJECT_ID);
}

ProtocolService::~ProtocolService() {
    Stop();
    spdlog::info("ProtocolService destroyed");
}

bool ProtocolService::Initialize() {
    if (is_initialized_) {
        return true;
    }

    if (!mediator_) {
        spdlog::error("Mediator is null");
        return false;
    }

    // 初始化Observer（包含注册到Mediator）
    auto observer_result = observer_->Init();
    if (!observer_result) {
        spdlog::error("Failed to initialize observer: {}", static_cast<int>(observer_result.error()));
        return false;
    }

    // 初始化Subject（包含注册到Mediator）
    auto subject_result = subject_->Init();
    if (!subject_result) {
        spdlog::error("Failed to initialize subject: {}", static_cast<int>(subject_result.error()));
        return false;
    }

    is_initialized_ = true;
    spdlog::info("ProtocolService initialized");
    return true;
}

bool ProtocolService::Start() {
    if (!is_initialized_) {
        spdlog::error("Service not initialized");
        return false;
    }

    if (is_running_) {
        return true;
    }

    should_stop_ = false;

    // 启动业务处理线程
    business_thread_ = std::thread(&ProtocolService::BusinessMessageProcessorLoop, this);

    // 启动事件上报线程（参考原始EventOperation）
    event_reporting_ = true;
    event_report_thread_ = std::thread(&ProtocolService::EventReportThreadFunc, this);

    is_running_ = true;
    spdlog::info("ProtocolService started with event reporting");
    return true;
}

bool ProtocolService::Stop() {
    if (!is_running_) {
        return true;
    }

    should_stop_ = true;

    // 通知线程退出
    common_queue_cv_.notify_all();

    // 停止事件上报线程
    event_reporting_ = false;

    // 等待线程结束
    if (business_thread_.joinable()) {
        business_thread_.join();
    }

    if (event_report_thread_.joinable()) {
        event_report_thread_.join();
    }

    is_running_ = false;
    spdlog::info("ProtocolService stopped");
    return true;
}

void ProtocolService::SetProtocolTransform(std::unique_ptr<transform::ProtocolTransform> transform) {
    protocol_transform_ = std::move(transform);
    spdlog::info("ProtocolService set protocol transform");
}

// 事件通过Subject的SendEventNotify方法发送，无需单独的回调设置方法

// 消息处理回调
void ProtocolService::OnCommonMessage(const base::CommonMessage& message) {
    spdlog::debug("Service received CommonMessage from {}", message.source_id);

    // 将消息加入队列
    PushToCommonQueue(message);
}

void ProtocolService::OnEventMessage(const base::EventMessage& message) {
    spdlog::debug("Service received EventMessage");

    // 暂时空实现
    // TODO: 处理事件消息
}

// 线程处理函数
void ProtocolService::BusinessMessageProcessorLoop() {
    spdlog::info("Service business processor started");

    while (!should_stop_) {
        base::CommonMessage message;

        // 从队列中取消息
        if (PopFromCommonQueue(message)) {
            // 处理业务逻辑
            ProcessBusinessMessage(message);
        }
    }

    spdlog::info("Service business processor stopped");
}

// 业务消息处理（核心方法）
void ProtocolService::ProcessBusinessMessage(const base::CommonMessage& message) {
    spdlog::debug("Processing business message: invoke_id={}", message.invoke_id);

    // 尝试解析输入的IEC103消息
    base::Message input_msg;
    size_t parsed = input_msg.deserialize(message.data);

    if (parsed > 0) {
        // 成功解析为IEC103消息
        spdlog::debug("Parsed input IEC103 message: TYP={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
                     input_msg.getTyp(), input_msg.getVsq(), input_msg.getCot(), input_msg.getFun(), input_msg.getInf());

        // 根据 Type 决定发送单帧还是多帧响应
        if (input_msg.getTyp() == 1) {
            // Type 1: 单帧测试，发送一个响应
            SendSingleResponse(message, input_msg);
        } else if (input_msg.getTyp() == 2) {
            // Type 2: 多帧测试，发送两个响应
            SendMultiFrameResponse(message, input_msg);
        } else {
            // 其他类型，默认单帧响应
            SendSingleResponse(message, input_msg);
        }
    } else {
        // 不能解析为IEC103，生成简单响应
        SendSimpleResponse(message);
    }
}

void ProtocolService::SendSingleResponse(const base::CommonMessage& original_message, const base::Message& input_msg) {
    spdlog::debug("Sending single response for Type 1");

    base::CommonMessage response;
    response.type = base::MessageType::RESULT;
    response.source_id = base::SERVICE_SUBJECT_ID;
    response.target_id = original_message.source_id;
    response.invoke_id = original_message.invoke_id;
    response.b_lastmsg = true;  // 单帧响应，肯定是最后一帧

    // 创建响应消息
    base::Message response_msg;
    response_msg.setTyp(input_msg.getTyp());
    response_msg.setVsq(input_msg.getVsq());
    response_msg.setCot(0x07);  // 激活确认
    response_msg.setSource(input_msg.getTarget());
    response_msg.setTarget(input_msg.getSource());
    response_msg.setFun(input_msg.getFun());
    response_msg.setInf(input_msg.getInf());

    // 设置响应内容
    std::string response_content = "SINGLE_RESPONSE_TO_" + input_msg.getTextContent();
    response_msg.setTextContent(response_content);

    // 序列化响应消息
    std::vector<uint8_t> serialized_response;
    size_t size = response_msg.serialize(serialized_response);

    if (size > 0) {
        response.data = serialized_response;
        spdlog::debug("Generated single IEC103 response message ({} bytes)", size);
    } else {
        // 序列化失败，使用简单响应
        std::string simple_response = "SINGLE_RESPONSE_FOR_" + original_message.invoke_id;
        response.data.assign(simple_response.begin(), simple_response.end());
    }

    // 发送响应
    if (subject_) {
        auto result = subject_->SendResult(response);
        if (result) {
            spdlog::debug("Sent single response to Gateway: invoke_id={}", response.invoke_id);
        } else {
            spdlog::error("Failed to send single response to Gateway: invoke_id={}", response.invoke_id);
        }
    }
}

void ProtocolService::SendMultiFrameResponse(const base::CommonMessage& original_message, const base::Message& input_msg) {
    spdlog::debug("Sending multi-frame response for Type 2");

    // 第一帧响应 (b_lastmsg = false)
    base::CommonMessage response1;
    response1.type = base::MessageType::RESULT;
    response1.source_id = base::SERVICE_SUBJECT_ID;
    response1.target_id = original_message.source_id;
    response1.invoke_id = original_message.invoke_id;
    response1.b_lastmsg = false;  // 不是最后一帧

    // 创建第一帧响应消息
    base::Message response_msg1;
    response_msg1.setTyp(input_msg.getTyp());
    response_msg1.setVsq(input_msg.getVsq());
    response_msg1.setCot(0x07);  // 激活确认
    response_msg1.setSource(input_msg.getTarget());
    response_msg1.setTarget(input_msg.getSource());
    response_msg1.setFun(input_msg.getFun());
    response_msg1.setInf(input_msg.getInf());

    // 设置第一帧响应内容
    std::string response_content1 = "MULTI_FRAME_1_TO_" + input_msg.getTextContent();
    response_msg1.setTextContent(response_content1);

    // 序列化第一帧响应消息
    std::vector<uint8_t> serialized_response1;
    size_t size1 = response_msg1.serialize(serialized_response1);

    if (size1 > 0) {
        response1.data = serialized_response1;
    } else {
        std::string simple_response1 = "MULTI_FRAME_1_FOR_" + original_message.invoke_id;
        response1.data.assign(simple_response1.begin(), simple_response1.end());
    }

    // 第二帧响应 (b_lastmsg = true)
    base::CommonMessage response2;
    response2.type = base::MessageType::RESULT;
    response2.source_id = base::SERVICE_SUBJECT_ID;
    response2.target_id = original_message.source_id;
    response2.invoke_id = original_message.invoke_id;
    response2.b_lastmsg = true;  // 最后一帧

    // 创建第二帧响应消息
    base::Message response_msg2;
    response_msg2.setTyp(input_msg.getTyp());
    response_msg2.setVsq(input_msg.getVsq());
    response_msg2.setCot(0x07);  // 激活确认
    response_msg2.setSource(input_msg.getTarget());
    response_msg2.setTarget(input_msg.getSource());
    response_msg2.setFun(input_msg.getFun());
    response_msg2.setInf(input_msg.getInf());

    // 设置第二帧响应内容
    std::string response_content2 = "MULTI_FRAME_2_TO_" + input_msg.getTextContent();
    response_msg2.setTextContent(response_content2);

    // 序列化第二帧响应消息
    std::vector<uint8_t> serialized_response2;
    size_t size2 = response_msg2.serialize(serialized_response2);

    if (size2 > 0) {
        response2.data = serialized_response2;
    } else {
        std::string simple_response2 = "MULTI_FRAME_2_FOR_" + original_message.invoke_id;
        response2.data.assign(simple_response2.begin(), simple_response2.end());
    }

    // 按顺序发送两帧响应
    if (subject_) {
        // 发送第一帧
        auto result1 = subject_->SendResult(response1);
        if (result1) {
            spdlog::debug("Sent first frame response to Gateway: invoke_id={}, b_lastmsg=false", response1.invoke_id);
        } else {
            spdlog::error("Failed to send first frame response to Gateway: invoke_id={}", response1.invoke_id);
            return;
        }

        // 发送第二帧
        auto result2 = subject_->SendResult(response2);
        if (result2) {
            spdlog::debug("Sent second frame response to Gateway: invoke_id={}, b_lastmsg=true", response2.invoke_id);
        } else {
            spdlog::error("Failed to send second frame response to Gateway: invoke_id={}", response2.invoke_id);
        }
    }
}

void ProtocolService::SendSimpleResponse(const base::CommonMessage& original_message) {
    spdlog::debug("Sending simple response for non-IEC103 message");

    base::CommonMessage response;
    response.type = base::MessageType::RESULT;
    response.source_id = base::SERVICE_SUBJECT_ID;
    response.target_id = original_message.source_id;
    response.invoke_id = original_message.invoke_id;
    response.b_lastmsg = true;  // 简单响应，肯定是最后一帧

    // 生成简单响应
    std::string simple_response = "SIMPLE_RESPONSE_FOR_" + original_message.invoke_id;
    response.data.assign(simple_response.begin(), simple_response.end());

    // 发送响应
    if (subject_) {
        auto result = subject_->SendResult(response);
        if (result) {
            spdlog::debug("Sent simple response to Gateway: invoke_id={}", response.invoke_id);
        } else {
            spdlog::error("Failed to send simple response to Gateway: invoke_id={}", response.invoke_id);
        }
    }
}

// 队列管理
bool ProtocolService::PushToCommonQueue(const base::CommonMessage& message) {
    std::lock_guard<std::mutex> lock(common_queue_mutex_);
    common_queue_.push(message);
    common_queue_cv_.notify_one();
    return true;
}

bool ProtocolService::PopFromCommonQueue(base::CommonMessage& message) {
    std::unique_lock<std::mutex> lock(common_queue_mutex_);

    // 等待消息或停止信号
    common_queue_cv_.wait(lock, [this] {
        return !common_queue_.empty() || should_stop_;
    });

    if (should_stop_ && common_queue_.empty()) {
        return false;
    }

    if (!common_queue_.empty()) {
        message = common_queue_.front();
        common_queue_.pop();
        return true;
    }

    return false;
}

// 事件上报线程实现（参考原始EventOperation）
void ProtocolService::EventReportThreadFunc() {
    spdlog::info("ProtocolService: Event report thread started");

    while (event_reporting_) {
        try {
            // 每10秒生成一次时间戳事件
            std::this_thread::sleep_for(std::chrono::seconds(10));

            if (!event_reporting_) {
                break;
            }

            // 生成时间戳事件
            GenerateTimestampEvent();

        } catch (const std::exception& e) {
            spdlog::error("ProtocolService: Exception in event report thread: {}", e.what());
        }
    }

    spdlog::info("ProtocolService: Event report thread stopped");
}

void ProtocolService::GenerateTimestampEvent() {
    // 创建时间戳事件（参考原始EventMessage结构）
    base::EventMessage event_msg;
    event_msg.event_type = 3;  // 时间戳事件类型
    event_msg.source_id = base::SERVICE_SUBJECT_ID;  // Service作为事件源
    event_msg.description = "Automatic timestamp event report";

    // 不在这里添加时间戳，让Transform在转换时添加（参考原始架构）
    event_msg.data.clear();  // 空数据，时间戳由Transform添加

    spdlog::debug("ProtocolService: Generated timestamp event: event_type={}, description={}",
                 event_msg.event_type, event_msg.description);

    // 通过Subject的SendEventNotify方法发送事件（解耦合的方式）
    if (subject_) {
        auto result = subject_->SendEventNotify(event_msg);
        if (result) {
            spdlog::debug("ProtocolService: Sent timestamp event via Subject");
        } else {
            spdlog::error("ProtocolService: Failed to send event via Subject: {}",
                         static_cast<int>(result.error()));
        }
    } else {
        spdlog::warn("ProtocolService: No Subject available, event not sent");
    }
}

} // namespace service
} // namespace protocol
} // namespace zexuan