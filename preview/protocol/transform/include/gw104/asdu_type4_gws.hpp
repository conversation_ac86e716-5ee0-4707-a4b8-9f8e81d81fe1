#pragma once

#include "asdu_base.hpp"

namespace zexuan {
namespace protocol {
namespace transform {
namespace gw104 {

/**
 * @brief GW104 ASDU Type4 处理类（本地直接处理）
 * 用于直接从本地获取数据并生成响应，支持时间戳发送
 * 不需要线程池，单线程处理即可
 */
class AsduType4GWS : public AsduBase {
public:
    AsduType4GWS() = default;
    virtual ~AsduType4GWS() = default;

    // Type4专门用于本地直接处理，不处理CommonMessage和EventMessage转换
    // 使用基类的默认实现（返回-1表示不支持）

    /**
     * @brief 直接从本地获取数据并生成协议帧（本地处理专用）
     * @param request_frame 请求协议帧
     * @param response_frames 输出的响应协议帧列表
     * @return 成功返回0，失败返回错误码
     */
    virtual int DirectResponseFromLocal(const base::ProtocolFrame& request_frame,
                                       base::ProtocolFrameList& response_frames) override;

    /**
     * @brief 获取支持的ASDU类型
     * @return ASDU类型标识
     */
    virtual uint8_t GetSupportedType() const override {
        return 4; // Type 4 = 本地直接处理
    }

    /**
     * @brief 获取ASDU类型描述
     * @return 类型描述字符串
     */
    virtual const char* GetTypeDescription() const override {
        return "GW104 ASDU Type4 - Direct Local Response with Timestamp";
    }

private:
    // 注意：现在直接在 DirectResponseFromLocal 中使用 Message 类构建数据
    // 不再需要单独的私有方法
};

} // namespace gw104
} // namespace transform
} // namespace protocol
} // namespace zexuan