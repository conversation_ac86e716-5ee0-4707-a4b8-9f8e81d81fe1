#include "gw104/gw104_transform.hpp"
#include "gw104/asdu_type1_gws.hpp"
#include "gw104/asdu_type2_gws.hpp"
#include "gw104/asdu_type3_gws.hpp"
#include "gw104/asdu_type4_gws.hpp"
#include "zexuan/base/base_types.hpp"
#include <spdlog/spdlog.h>
#include <cstring>

namespace zexuan {
namespace protocol {
namespace transform {
namespace gw104 {

GW104Transform::GW104Transform() {
    spdlog::info("GW104Transform created");
    InitializeAsduHandlers();
}

int GW104Transform::ConvertProToCommonMsg(
    const base::ProtocolFrameList& frame_list,
    std::vector<base::CommonMessage>& common_list,
    base::ProtocolFrameList& result_frames) {

    // 循环处理每一帧（参考原始实现）
    for (const auto& frame : frame_list) {
        if (!ValidateFrame(frame.data)) {
            spdlog::error("Invalid protocol frame");
            // 创建错误回应帧
            base::ProtocolFrame error_frame;
            error_frame.type = frame.type;
            error_frame.cot = 47; // 否定确认
            error_frame.asdu_addr = frame.asdu_addr;
            result_frames.push_back(error_frame);
            continue;
        }

        // 根据ProtocolFrame的type字段获取对应的ASDU处理器（参考原始基类指针模式）
        AsduBase* handler = GetAsduHandler(frame.type);
        if (!handler) {
            spdlog::error("No handler for ASDU type {}", frame.type);
            // 创建不支持的类型错误回应
            base::ProtocolFrame error_frame;
            error_frame.type = frame.type;
            error_frame.cot = 44; // 类型标识未知
            error_frame.asdu_addr = frame.asdu_addr;
            result_frames.push_back(error_frame);
            continue;
        }

        // 使用ASDU处理器转换（参考原始虚函数调用模式）
        // 直接传入整个ProtocolFrame
        base::CommonMessage common_msg;
        int result = handler->ParseToCommonMessage(frame, common_msg);
        if (result == 0) {
            common_list.push_back(common_msg);
            spdlog::debug("Successfully converted ASDU type {} using handler: {}",
                         frame.type, handler->GetTypeDescription());
        } else {
            spdlog::error("Failed to convert ASDU type {} using handler", frame.type);
            // 创建转换失败错误回应
            base::ProtocolFrame error_frame;
            error_frame.type = frame.type;
            error_frame.cot = 47; // 否定确认
            error_frame.asdu_addr = frame.asdu_addr;
            result_frames.push_back(error_frame);
        }
    }

    return 0;
}

// 其他方法的空实现
int GW104Transform::ConvertProToEventMsg(
    const base::ProtocolFrame& frame,
    std::vector<base::EventMessage>& event_list) {
    // 暂时空实现
    spdlog::debug("ConvertProToEventMsg not implemented yet");
    return 0;
}

int GW104Transform::ConvertEventMsgToPro(
    const base::EventMessage& event_msg,
    base::ProtocolFrameList& frame_list) {

    spdlog::debug("Converting EventMessage to ProtocolFrame: event_type={}, source_id={}, data_size={}",
                 event_msg.event_type, event_msg.source_id, event_msg.data.size());

    // 根据EventMessage的类型确定ASDU类型（这里简化为Type3时间戳事件）
    uint8_t asdu_type = 3; // 使用Type3处理时间戳事件

    // 获取对应的ASDU处理器
    AsduBase* handler = GetAsduHandler(asdu_type);
    if (!handler) {
        spdlog::error("No handler for ASDU type {}", asdu_type);
        return -1;
    }

    // 使用基类接口将EventMessage转换为ProtocolFrame
    base::ProtocolFrame result_frame;
    int result = handler->ConvertFromEventMessage(event_msg, result_frame);
    if (result != 0) {
        spdlog::error("Handler for ASDU type {} does not support EventMessage conversion or conversion failed", asdu_type);
        return result;
    }
    if (result != 0) {
        spdlog::error("Failed to convert EventMessage using ASDU Type3 handler: result={}", result);
        return result;
    }

    // 添加到结果帧列表
    frame_list.push_back(result_frame);

    spdlog::debug("Successfully converted EventMessage to ProtocolFrame: frame_type={}, data_size={}",
                 result_frame.type, result_frame.data.size());

    return 0;
}

int GW104Transform::ConvertCommonMsgToPro(
    const base::CommonMessage& common_msg,
    base::ProtocolFrameList& cmd_frames,
    base::ProtocolFrameList& result_frames) {

    spdlog::debug("Converting CommonMessage to ProtocolFrame: source_id={}, target_id={}, data_size={}",
                 common_msg.source_id, common_msg.target_id, common_msg.data.size());

    // 根据CommonMessage的类型或其他字段确定ASDU类型
    // 这里简化处理，可以根据实际需求扩展
    uint8_t asdu_type = 1; // 默认使用Type1，实际应该根据消息内容确定

    // 获取对应的ASDU处理器
    AsduBase* handler = GetAsduHandler(asdu_type);
    if (!handler) {
        spdlog::error("No handler for ASDU type {}", asdu_type);
        return -1;
    }

    // 使用ASDU处理器将CommonMessage转换为ProtocolFrame
    base::ProtocolFrame result_frame;
    int result = handler->ConvertFromCommonMessage(common_msg, result_frame);
    if (result != 0) {
        spdlog::error("Failed to convert CommonMessage using ASDU handler: result={}", result);
        return result;
    }

    // 添加到结果帧列表
    result_frames.push_back(result_frame);

    spdlog::debug("Successfully converted CommonMessage to ProtocolFrame: frame_type={}, data_size={}",
                 result_frame.type, result_frame.data.size());

    return 0;
}

// 协议转换方法实现

base::ProtocolConvertType GW104Transform::GetConvertTypeByFrame(const base::ProtocolFrame& frame) {
    // 参考原始 GetCvtTypeByProInf 实现
    switch (frame.type) {
        case 1:  // ASDU1 - 单帧测试
            return GetAsdu1ConvertType(frame);
        case 2: // ASDU2 - 多帧测试
            return GetAsdu2ConvertType(frame);
        case 4: // ASDU4 - 本地直接处理
            return base::ProtocolConvertType::FROM_LOCAL;
        default:
            return GetDefaultConvertType(frame);
    }
}



// 核心辅助方法实现

// 私有方法实现
bool GW104Transform::ValidateFrame(const std::vector<uint8_t>& data) {
    if (data.size() < IEC104_APCI_LENGTH) {
        return false;
    }

    if (data[0] != IEC104_START_BYTE) {
        return false;
    }

    uint16_t apdu_length = data[1];
    if (data.size() < apdu_length + 2) {
        return false;
    }

    return true;
}

// 协议帧类型判断方法实现（参考原始 _GetAsduXXCvtType 系列方法）

base::ProtocolConvertType GW104Transform::GetAsdu1ConvertType(const base::ProtocolFrame& frame) {
    return base::ProtocolConvertType::TO_CALL;
}

base::ProtocolConvertType GW104Transform::GetAsdu2ConvertType(const base::ProtocolFrame& frame) {
    return base::ProtocolConvertType::TO_CTRL;
}

base::ProtocolConvertType GW104Transform::GetDefaultConvertType(const base::ProtocolFrame& frame) {
    return base::ProtocolConvertType::UNKNOWN;
}



void GW104Transform::InitializeAsduHandlers() {
    // 初始化ASDU处理器（参考原始基类指针模式）
    asdu_handlers_[1] = std::make_unique<AsduType1GWS>();
    asdu_handlers_[2] = std::make_unique<AsduType2GWS>();
    asdu_handlers_[3] = std::make_unique<AsduType3GWS>();  // 时间戳事件上报
    asdu_handlers_[4] = std::make_unique<AsduType4GWS>();  // 本地直接处理

    spdlog::info("GW104Transform: Initialized {} ASDU handlers", asdu_handlers_.size());
}

AsduBase* GW104Transform::GetAsduHandler(uint8_t type) {
    auto it = asdu_handlers_.find(type);
    if (it != asdu_handlers_.end()) {
        return it->second.get();
    }

    spdlog::warn("GW104Transform: No handler found for ASDU type {}", type);
    return nullptr;
}

int GW104Transform::DirectResponseFromLocal(const base::ProtocolFrame& request_frame,
                                           base::ProtocolFrameList& response_frames) {
    spdlog::info("GW104Transform: Processing direct local response for frame type {}", request_frame.type);

    // 获取对应的ASDU处理器
    AsduBase* handler = GetAsduHandler(request_frame.type);
    if (!handler) {
        spdlog::error("GW104Transform: No handler found for ASDU type {}", request_frame.type);
        return -1;
    }

    // 调用处理器的本地直接处理方法
    int result = handler->DirectResponseFromLocal(request_frame, response_frames);
    if (result != 0) {
        spdlog::error("GW104Transform: Handler failed to process local response for type {}, result={}",
                     request_frame.type, result);
        return result;
    }

    spdlog::info("GW104Transform: Successfully processed direct local response, generated {} response frames",
                response_frames.size());
    return 0;
}

} // namespace gw104
} // namespace transform
} // namespace protocol
} // namespace zexuan
